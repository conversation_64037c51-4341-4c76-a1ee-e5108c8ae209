# 🔌 智能睡眠音频评估系统 - 插件开发指南

## 📋 目录

1. [概述](#概述)
2. [插件系统架构](#插件系统架构)
3. [开发环境准备](#开发环境准备)
4. [创建报告生成器插件](#创建报告生成器插件)
5. [创建分析插件](#创建分析插件)
6. [插件配置和验证](#插件配置和验证)
7. [测试和调试](#测试和调试)
8. [部署和分发](#部署和分发)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

---

## 概述

智能睡眠音频评估系统的插件系统允许开发者扩展系统功能，主要支持两种类型的插件：

- **报告生成器插件**: 生成不同格式的分析报告（HTML、PDF、Excel等）
- **分析插件**: 提供额外的音频分析功能

### 插件系统特性

- ✅ **动态加载**: 运行时自动发现和加载插件
- ✅ **类型安全**: 基于抽象基类的强类型接口
- ✅ **配置验证**: 插件配置参数验证机制
- ✅ **错误处理**: 完善的错误处理和降级机制
- ✅ **生命周期管理**: 插件初始化、清理和资源管理
- ✅ **依赖检查**: 自动检查插件所需的依赖库

---

## 插件系统架构

```
plugin_system.py
├── PluginInterface (基础接口)
├── ReportGeneratorPlugin (报告生成器基类)
├── AnalysisPlugin (分析插件基类)
├── PluginRegistry (插件注册表)
└── PluginManager (插件管理器)

plugins/
├── __init__.py
├── html_report_generator.py
├── pdf_report_generator.py
├── csv_report_generator.py
└── your_custom_plugin.py
```

### 核心组件

1. **PluginInterface**: 所有插件的基础接口
2. **ReportGeneratorPlugin**: 报告生成器插件基类
3. **AnalysisPlugin**: 分析插件基类
4. **PluginRegistry**: 管理插件注册和发现
5. **PluginManager**: 处理插件加载、实例化和生命周期

---

## 开发环境准备

### 1. 环境要求

- Python 3.8+
- 智能睡眠音频评估系统核心库

### 2. 目录结构

```bash
your_project/
├── plugin_system.py          # 插件系统核心
├── plugins/                  # 插件目录
│   ├── __init__.py
│   └── your_plugin.py       # 你的插件文件
├── run_sleep_audio_analysis.py  # 主程序
└── docs/                    # 文档
```

### 3. 依赖安装

根据插件类型安装相应依赖：

```bash
# HTML报告插件（无额外依赖）
# PDF报告插件
pip install reportlab

# Excel报告插件
pip install openpyxl

# 图表功能
pip install matplotlib seaborn
```

---

## 创建报告生成器插件

### 1. 基本结构

```python
# plugins/my_report_generator.py
import sys
from pathlib import Path

# 导入插件基类
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin

class MyReportGenerator(ReportGeneratorPlugin):
    """自定义报告生成器插件"""
    
    def get_plugin_info(self):
        """返回插件信息"""
        return {
            'name': 'MyReportGenerator',
            'version': '1.0',
            'description': '我的自定义报告生成器',
            'author': '你的名字',
            'supported_formats': ['myformat'],
            'dependencies': []  # 所需依赖库列表
        }
    
    def validate_config(self, config):
        """验证配置参数"""
        # 在这里验证配置参数
        return True
    
    def get_supported_formats(self):
        """返回支持的输出格式"""
        return ['myformat']
    
    def generate_report(self, results, config):
        """生成报告的核心方法"""
        try:
            # 在这里实现报告生成逻辑
            report_content = self._create_report_content(results, config)
            
            # 如果配置了输出路径，保存到文件
            output_path = config.get('output_path')
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                return f"报告已保存到: {output_path}"
            else:
                return report_content
                
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            return f"错误: {str(e)}"
    
    def _create_report_content(self, results, config):
        """创建报告内容的辅助方法"""
        lines = []
        lines.append("=== 我的自定义报告 ===")
        lines.append(f"分析文件数: {len(results)}")
        
        for i, result in enumerate(results, 1):
            filename = Path(result.audio_file).name
            score = result.sleep_suitability.overall_score
            lines.append(f"{i}. {filename}: {score:.1f}/100")
        
        return "\n".join(lines)
```

### 2. 高级功能示例

```python
class AdvancedReportGenerator(ReportGeneratorPlugin):
    """高级报告生成器示例"""
    
    def __init__(self, config=None):
        super().__init__(config)
        # 检查依赖
        self.check_dependencies()
    
    def check_dependencies(self):
        """检查依赖库"""
        try:
            import matplotlib
            self.matplotlib_available = True
        except ImportError:
            self.matplotlib_available = False
            self.logger.warning("matplotlib不可用，图表功能将被禁用")
    
    def get_plugin_info(self):
        return {
            'name': 'AdvancedReportGenerator',
            'version': '2.0',
            'description': '支持图表的高级报告生成器',
            'dependencies': ['matplotlib'],
            'available': self.matplotlib_available
        }
    
    def validate_config(self, config):
        """高级配置验证"""
        required_params = ['title', 'output_format']
        for param in required_params:
            if param not in config:
                self.logger.error(f"缺少必需参数: {param}")
                return False
        
        # 验证输出格式
        valid_formats = self.get_supported_formats()
        if config['output_format'] not in valid_formats:
            self.logger.error(f"不支持的输出格式: {config['output_format']}")
            return False
        
        return True
    
    def generate_report(self, results, config):
        """生成高级报告"""
        if not self.validate_config(config):
            return "配置验证失败"
        
        # 根据配置生成不同类型的报告
        output_format = config['output_format']
        
        if output_format == 'html_with_charts':
            return self._generate_html_with_charts(results, config)
        elif output_format == 'summary':
            return self._generate_summary_report(results, config)
        else:
            return self._generate_basic_report(results, config)
```

---

## 创建分析插件

### 1. 基本结构

```python
# plugins/my_analysis_plugin.py
from plugin_system import AnalysisPlugin

class MyAnalysisPlugin(AnalysisPlugin):
    """自定义分析插件"""
    
    def get_plugin_info(self):
        return {
            'name': 'MyAnalysisPlugin',
            'version': '1.0',
            'description': '我的自定义音频分析插件',
            'analysis_type': 'custom_analysis'
        }
    
    def validate_config(self, config):
        """验证分析配置"""
        return True
    
    def get_analysis_type(self):
        """返回分析类型"""
        return 'custom_analysis'
    
    def analyze(self, audio_data, config):
        """执行音频分析"""
        try:
            # 在这里实现你的分析逻辑
            analysis_result = {
                'custom_metric': self._calculate_custom_metric(audio_data),
                'analysis_timestamp': datetime.now().isoformat(),
                'config_used': config
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_custom_metric(self, audio_data):
        """计算自定义指标"""
        # 实现你的分析算法
        return 42.0  # 示例返回值
```

---

## 插件配置和验证

### 1. 配置文件格式

创建 `plugin_config.json`:

```json
{
    "MyReportGenerator": {
        "title": "自定义分析报告",
        "output_format": "html_with_charts",
        "include_charts": true,
        "chart_style": "modern",
        "color_scheme": "blue"
    },
    "PDFReportGenerator": {
        "page_size": "A4",
        "font_size": 12,
        "include_logo": true
    }
}
```

### 2. 配置验证最佳实践

```python
def validate_config(self, config):
    """配置验证示例"""
    # 1. 检查必需参数
    required_params = ['title', 'output_format']
    for param in required_params:
        if param not in config:
            self.logger.error(f"缺少必需参数: {param}")
            return False
    
    # 2. 验证参数类型
    if not isinstance(config.get('include_charts', True), bool):
        self.logger.error("include_charts 必须是布尔值")
        return False
    
    # 3. 验证参数值范围
    font_size = config.get('font_size', 12)
    if not (8 <= font_size <= 24):
        self.logger.error("font_size 必须在 8-24 之间")
        return False
    
    # 4. 验证依赖
    if config.get('include_charts') and not self.matplotlib_available:
        self.logger.error("图表功能需要 matplotlib 库")
        return False
    
    return True
```

---

## 测试和调试

### 1. 单元测试

```python
# test_my_plugin.py
import unittest
from plugins.my_report_generator import MyReportGenerator

class TestMyReportGenerator(unittest.TestCase):
    
    def setUp(self):
        self.plugin = MyReportGenerator()
    
    def test_plugin_info(self):
        """测试插件信息"""
        info = self.plugin.get_plugin_info()
        self.assertEqual(info['name'], 'MyReportGenerator')
        self.assertEqual(info['version'], '1.0')
    
    def test_config_validation(self):
        """测试配置验证"""
        valid_config = {'title': 'Test Report'}
        self.assertTrue(self.plugin.validate_config(valid_config))
    
    def test_report_generation(self):
        """测试报告生成"""
        mock_results = [MockResult()]
        config = {'title': 'Test Report'}
        
        report = self.plugin.generate_report(mock_results, config)
        self.assertIn('Test Report', report)

if __name__ == '__main__':
    unittest.main()
```

### 2. 调试技巧

```python
# 在插件中添加调试日志
def generate_report(self, results, config):
    self.logger.debug(f"开始生成报告，结果数量: {len(results)}")
    self.logger.debug(f"配置参数: {config}")
    
    try:
        # 报告生成逻辑
        report = self._create_report(results, config)
        self.logger.info("报告生成成功")
        return report
    except Exception as e:
        self.logger.error(f"报告生成失败: {e}", exc_info=True)
        raise
```

### 3. 集成测试

```bash
# 测试插件加载
python -c "
from plugin_system import get_plugin_manager
manager = get_plugin_manager()
print(manager.list_available_plugins())
"

# 测试报告生成
python run_sleep_audio_analysis.py noisekun/waves.ogm --plugin MyReportGenerator --plugin-config plugin_config.json

---

## 部署和分发

### 1. 插件打包

创建插件包结构：
```
my_plugin_package/
├── setup.py
├── README.md
├── requirements.txt
└── my_plugin/
    ├── __init__.py
    └── my_report_generator.py
```
`setup.py` 示例：
```python
from setuptools import setup, find_packages

setup(
    name="my-sleep-audio-plugin",
    version="1.0.0",
    description="自定义睡眠音频分析报告插件",
    author="你的名字",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "reportlab>=3.5.0",  # 如果需要
    ],
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
    ],
)
```
### 2. 安装插件
```bash
# 从源码安装
pip install -e .

# 从PyPI安装
pip install my-sleep-audio-plugin

# 复制插件文件到plugins目录
cp my_plugin/my_report_generator.py /path/to/sleep_system/plugins/
```
### 3. 插件发现机制

系统会自动扫描以下位置的插件：

1. `plugins/` 目录（相对于主程序）
2. 用户指定的插件目录
3. 已安装的Python包中的插件

---

## 最佳实践

### 1. 代码规范
```python
class WellDesignedPlugin(ReportGeneratorPlugin):
    """设计良好的插件示例"""

    def __init__(self, config=None):
        super().__init__(config)
        self._validate_dependencies()
        self._setup_logging()

    def _validate_dependencies(self):
        """验证依赖库"""
        self.dependencies_ok = True
        for dep in self.get_required_dependencies():
            try:
                __import__(dep)
            except ImportError:
                self.logger.error(f"缺少依赖库: {dep}")
                self.dependencies_ok = False

    def _setup_logging(self):
        """设置日志"""
        self.logger.setLevel(logging.INFO)

    def get_required_dependencies(self):
        """返回所需依赖"""
        return ['matplotlib', 'pandas']

    def validate_config(self, config):
        """验证配置"""
        if not self.dependencies_ok:
            return False

        # 详细的配置验证逻辑
        return self._validate_config_schema(config)

    def _validate_config_schema(self, config):
        """验证配置模式"""
        schema = {
            'title': str,
            'include_charts': bool,
            'output_format': str
        }

        for key, expected_type in schema.items():
            if key in config and not isinstance(config[key], expected_type):
                self.logger.error(f"配置参数 {key} 类型错误，期望 {expected_type}")
                return False

        return True
```
### 2. 错误处理
```python
def generate_report(self, results, config):
    """带有完善错误处理的报告生成"""
    try:
        # 验证输入
        if not results:
            raise ValueError("没有分析结果")

        if not self.validate_config(config):
            raise ValueError("配置验证失败")

        # 生成报告
        report = self._create_report_content(results, config)

        # 保存文件（如果需要）
        output_path = config.get('output_path')
        if output_path:
            self._save_report(report, output_path)
            return f"报告已保存到: {output_path}"

        return report

    except ValueError as e:
        self.logger.error(f"输入验证错误: {e}")
        return f"错误: {e}"
    except IOError as e:
        self.logger.error(f"文件操作错误: {e}")
        return f"文件错误: {e}"
    except Exception as e:
        self.logger.error(f"未知错误: {e}", exc_info=True)
        return f"生成报告时发生错误: {e}"

def _save_report(self, content, output_path):
    """安全地保存报告"""
    try:
        # 确保目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        # 保存文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)

    except Exception as e:
        raise IOError(f"无法保存文件到 {output_path}: {e}")
```
### 3. 性能优化
```python
class OptimizedPlugin(ReportGeneratorPlugin):
    """性能优化的插件示例"""

    def __init__(self, config=None):
        super().__init__(config)
        self._template_cache = {}
        self._result_cache = {}

    def generate_report(self, results, config):
        """带缓存的报告生成"""
        # 生成缓存键
        cache_key = self._generate_cache_key(results, config)

        # 检查缓存
        if cache_key in self._result_cache:
            self.logger.debug("使用缓存的报告")
            return self._result_cache[cache_key]

        # 生成新报告
        report = self._create_report(results, config)

        # 缓存结果
        self._result_cache[cache_key] = report

        return report

    def _generate_cache_key(self, results, config):
        """生成缓存键"""
        import hashlib

        # 基于结果和配置生成哈希
        content = f"{len(results)}_{hash(str(config))}"
        return hashlib.md5(content.encode()).hexdigest()
```
---

## 常见问题

### Q1: 插件无法加载怎么办？

**A**: 检查以下几点：

1. 插件文件是否在正确的目录中
2. 插件类是否继承了正确的基类
3. 是否有语法错误或导入错误
4. 检查日志输出获取详细错误信息
```bash
# 启用调试日志
python run_sleep_audio_analysis.py --list-plugins --verbose
```
### Q2: 如何处理插件依赖？

**A**: 在插件中检查依赖并提供友好的错误信息：
```python
def __init__(self, config=None):
    super().__init__(config)
    try:
        import required_library
        self.library_available = True
    except ImportError:
        self.library_available = False
        self.logger.warning("请安装 required_library: pip install required_library")

def validate_config(self, config):
    if not self.library_available:
        return False
    return True
```
### Q3: 插件配置参数如何传递？

**A**: 通过命令行参数或配置文件：
```bash
# 使用配置文件
python run_sleep_audio_analysis.py input.wav --plugin MyPlugin --plugin-config config.json

# 配置文件内容
{
    "MyPlugin": {
        "param1": "value1",
        "param2": true
    }
}
```
### Q4: 如何调试插件？

**A**: 使用日志和异常处理：
```python
def generate_report(self, results, config):
    self.logger.debug(f"插件开始执行，参数: {config}")

    try:
        # 插件逻辑
        result = self._do_work(results, config)
        self.logger.info("插件执行成功")
        return result
    except Exception as e:
        self.logger.error(f"插件执行失败: {e}", exc_info=True)
        raise
```
### Q5: 插件性能优化建议？

**A**:

1. **使用缓存**: 缓存计算结果和模板
2. **延迟加载**: 只在需要时导入重型库
3. **批量处理**: 一次处理多个结果
4. **内存管理**: 及时释放大对象
```python
# 延迟导入示例
def _get_heavy_library(self):
    if not hasattr(self, '_heavy_lib'):
        import heavy_library
        self._heavy_lib = heavy_library
    return self._heavy_lib
```
---

## 📚 参考资源

- [插件系统API文档](./插件系统API文档.md)
- [示例插件代码](../plugins/)
- [测试用例](../test_plugin_system.py)
- [主程序集成](../run_sleep_audio_analysis.py)

---

**🎉 恭喜！你现在已经掌握了插件开发的基础知识。开始创建你的第一个插件吧！**
```
4 集成测试文档                                ██
```

---

## 🎯 里程碑与交付物

### 里程碑1: JSON Bug修复 (第1周末)
**交付物**:
- [ ] 修复后的run_sleep_audio_analysis.py
- [ ] 类型转换工具函数
- [ ] 回归测试报告
- [ ] 修复说明文档

### 里程碑2: 功能统一 (第2周末)
**交付物**:
- [ ] 统一的命令行工具
- [ ] 新功能使用文档
- [ ] 迁移指南
- [ ] 性能测试报告

### 里程碑3: 配置驱动 (第3周末)
**交付物**:
- [ ] 配置文件模板
- [ ] 配置管理系统
- [ ] 模板引擎
- [ ] 配置文档

### 里程碑4: 插件系统 (第4周末)
**交付物**:
- [ ] 插件框架
- [ ] 示例插件
- [ ] 插件开发指南
- [ ] 完整系统文档

---

## ⚠️ 风险评估与缓解措施

### 技术风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| **向后兼容性破坏** | 🟡 中等 | 现有用户受影响 | • 保持API兼容<br/>• 渐进式升级<br/>• 充分测试 |
| **性能回归** | 🟡 中等 | 分析速度下降 | • 性能基准测试<br/>• 代码优化<br/>• 监控关键指标 |
| **配置复杂度** | 🟢 低 | 用户学习成本 | • 提供默认配置<br/>• 详细文档<br/>• 示例模板 |
| **插件安全性** | 🟡 中等 | 系统安全风险 | • 插件沙箱<br/>• 权限控制<br/>• 代码审查 |

### 项目风险

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| **时间延期** | 🟡 中等 | 交付延迟 | • 任务优先级管理<br/>• 并行开发<br/>• 敏捷迭代 |
| **资源不足** | 🟢 低 | 功能削减 | • 分阶段交付<br/>• 核心功能优先<br/>• 外部支持 |
| **需求变更** | 🟡 中等 | 计划调整 | • 需求冻结<br/>• 变更控制<br/>• 影响评估 |

---

## 🛠️ 资源需求

### 人员技能要求

| 角色 | 技能要求 | 参与阶段 | 工作量 |
|------|----------|----------|--------|
| **系统架构师** | Python、系统设计、架构模式 | 全程 | 3.1人日 |
| **后端开发工程师** | Python、JSON、YAML、插件开发 | 全程 | 4.0人日 |
| **前端开发工程师** | HTML、CSS、Jinja2、Bootstrap | P2-P3 | 1.2人日 |
| **测试工程师** | 单元测试、集成测试、性能测试 | P1、P3 | 1.2人日 |
| **技术文档工程师** | 技术写作、API文档 | P3 | 0.5人日 |

### 技术栈要求

**核心技术**:
- Python 3.8+
- PyYAML (配置文件处理)
- Jinja2 (模板引擎)
- pytest (测试框架)

**可选技术**:
- Bootstrap (HTML报告样式)
- WeasyPrint (PDF生成)
- Sphinx (文档生成)

### 开发环境

**必需工具**:
- Git (版本控制)
- Python虚拟环境
- IDE (PyCharm/VSCode)
- 测试覆盖率工具

**推荐工具**:
- Docker (环境一致性)
- GitHub Actions (CI/CD)
- SonarQube (代码质量)

---

## 📋 验收标准总览

### 功能验收标准

1. **JSON序列化修复**:
   - [ ] 所有numpy类型正确序列化
   - [ ] 现有功能100%兼容
   - [ ] 性能无明显影响

2. **功能集成统一**:
   - [ ] 新功能正常工作
   - [ ] 参数接口设计合理
   - [ ] 用户体验提升

3. **配置驱动架构**:
   - [ ] 配置文件格式规范
   - [ ] 模板系统灵活可扩展
   - [ ] 热更新支持

4. **插件化扩展**:
   - [ ] 插件接口设计清晰
   - [ ] 插件管理器稳定
   - [ ] 示例插件可用

### 质量验收标准

- **代码质量**: 通过SonarQube检查，无严重问题
- **测试覆盖率**: ≥85%
- **性能指标**: 分析速度不低于现有版本的95%
- **文档完整性**: API文档、用户指南、开发指南齐全
- **安全性**: 通过安全扫描，无高危漏洞

---

## 🔄 迁移路径

### 现有用户迁移策略

1. **平滑升级**:
   - 保持现有命令行接口不变
   - 新功能通过可选参数提供
   - 提供迁移检查工具

2. **分阶段迁移**:
   - 第1阶段：bug修复，用户无感知
   - 第2阶段：新功能可选使用
   - 第3阶段：配置文件可选采用
   - 第4阶段：插件系统可选扩展

3. **向后兼容保证**:
   - 现有脚本无需修改
   - 输出格式保持一致
   - 性能不低于原版本

### 文档更新计划

1. **用户文档**:
   - 更新README.md
   - 新增功能使用指南
   - 提供迁移示例

2. **开发文档**:
   - API参考文档
   - 插件开发指南
   - 架构设计文档

3. **运维文档**:
   - 部署指南
   - 配置管理
   - 故障排查

---

## 💻 具体代码修改示例

### P0任务：JSON序列化修复示例

**文件**: `run_sleep_audio_analysis.py`

```python
# 在文件开头添加
import numpy as np

def convert_numpy_types(obj):
    """转换numpy类型为Python原生类型，解决JSON序列化问题"""
    if isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj

# 修改JSON输出部分
def output_json_format(results, output_file=None):
    """输出JSON格式结果"""
    try:
        # 转换numpy类型
        converted_results = convert_numpy_types(results)

        json_output = json.dumps(converted_results,
                                ensure_ascii=False,
                                indent=2)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_output)
        else:
            print(json_output)

    except Exception as e:
        print(f"JSON输出错误: {e}")
        # 降级处理：输出基本信息
        fallback_output = {
            "error": "JSON序列化失败",
            "message": str(e),
            "files_analyzed": len(results) if isinstance(results, list) else 1
        }
        print(json.dumps(fallback_output, ensure_ascii=False, indent=2))
```

### P1任务：参数扩展示例

```python
# 新增参数定义
parser.add_argument(
    '--format',
    choices=['text', 'json', 'markdown'],
    default='text',
    help='输出格式 (默认: text)'
)

parser.add_argument(
    '--auto-name',
    action='store_true',
    help='自动生成带时间戳的文件名'
)

parser.add_argument(
    '--comparison',
    action='store_true',
    help='生成技术参数对比表格（适用于多文件分析）'
)

parser.add_argument(
    '--template',
    choices=['standard', 'research', 'clinical', 'consumer'],
    default='standard',
    help='报告模板类型'
)

# 输出处理逻辑
def generate_output(results, args):
    """根据参数生成相应格式的输出"""

    # 确定输出文件路径
    output_path = args.output
    if args.auto_name and not output_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.format == 'markdown':
            output_path = f"analysis_report_{timestamp}.md"
        elif args.format == 'json':
            output_path = f"analysis_report_{timestamp}.json"
        else:
            output_path = f"analysis_report_{timestamp}.txt"

    # 生成输出内容
    if args.format == 'json':
        output_data = generate_json_output(results, args)
    elif args.format == 'markdown':
        output_data = generate_markdown_output(results, args)
    else:
        output_data = generate_text_output(results, args)

    # 输出结果
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(output_data)
        print(f"📄 报告已保存到: {output_path}")
    else:
        print(output_data)
```

### P2任务：配置系统示例

**文件**: `config/report_templates.yaml`

```yaml
system:
  version: "2.0"
  default_template: "standard"

templates:
  standard:
    title: "智能睡眠音频评估系统 - 分析报告"
    sections:
      - "summary"
      - "ranking"
      - "details"
      - "recommendations"
    colors:
      primary: "#2196F3"
      success: "#4CAF50"
      warning: "#FF9800"
      danger: "#F44336"

  research:
    title: "智能睡眠音频评估系统 - 科研分析报告"
    sections:
      - "summary"
      - "ranking"
      - "comparison"
      - "methodology"
      - "details"
    include_references: true
    scientific_focus: true

output_formats:
  markdown:
    extension: ".md"
    generator: "MarkdownReportGenerator"
    template_file: "templates/markdown_report.j2"

  json:
    extension: ".json"
    generator: "JSONReportGenerator"
    pretty_print: true
    include_metadata: true
```

**文件**: `config_manager.py`

```python
import yaml
from pathlib import Path

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_path="config/report_templates.yaml"):
        self.config_path = Path(config_path)
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_path}")
            return self.get_default_config()
        except yaml.YAMLError as e:
            print(f"配置文件格式错误: {e}")
            return self.get_default_config()

    def get_template(self, template_name):
        """获取报告模板配置"""
        templates = self.config.get('templates', {})
        return templates.get(template_name, templates.get('standard', {}))

    def get_output_format(self, format_name):
        """获取输出格式配置"""
        formats = self.config.get('output_formats', {})
        return formats.get(format_name, {})

    def get_default_config(self):
        """获取默认配置"""
        return {
            'system': {'version': '2.0', 'default_template': 'standard'},
            'templates': {
                'standard': {
                    'title': '智能睡眠音频评估系统 - 分析报告',
                    'sections': ['summary', 'ranking', 'details']
                }
            },
            'output_formats': {
                'text': {'extension': '.txt'},
                'json': {'extension': '.json'},
                'markdown': {'extension': '.md'}
            }
        }
```

### P3任务：插件系统示例

**文件**: `plugin_manager.py`

```python
import importlib
import inspect
from pathlib import Path
from abc import ABC, abstractmethod

class ReportGeneratorPlugin(ABC):
    """报告生成器插件基类"""

    @abstractmethod
    def generate(self, results, config):
        """生成报告的核心方法"""
        pass

    @abstractmethod
    def get_supported_formats(self):
        """返回支持的输出格式列表"""
        pass

    def validate_config(self, config):
        """验证配置的有效性"""
        return True

class PluginManager:
    """插件管理器"""

    def __init__(self, plugin_dir="plugins"):
        self.plugin_dir = Path(plugin_dir)
        self.plugins = {
            'report_generators': {},
            'analyzers': {}
        }
        self.load_plugins()

    def load_plugins(self):
        """动态加载插件"""
        if not self.plugin_dir.exists():
            self.plugin_dir.mkdir(parents=True)
            return

        for plugin_file in self.plugin_dir.glob("*.py"):
            if plugin_file.name.startswith('__'):
                continue
            self.load_plugin(plugin_file)

    def load_plugin(self, plugin_file):
        """加载单个插件文件"""
        try:
            spec = importlib.util.spec_from_file_location(
                plugin_file.stem, plugin_file
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 查找插件类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if issubclass(obj, ReportGeneratorPlugin) and obj != ReportGeneratorPlugin:
                    self.register_plugin(obj, 'report_generators')

        except Exception as e:
            print(f"加载插件失败 {plugin_file}: {e}")

    def register_plugin(self, plugin_class, plugin_type):
        """注册插件"""
        plugin_name = plugin_class.__name__
        self.plugins[plugin_type][plugin_name] = plugin_class
        print(f"插件已注册: {plugin_name}")

    def get_plugin(self, plugin_type, plugin_name):
        """获取插件实例"""
        if plugin_type in self.plugins and plugin_name in self.plugins[plugin_type]:
            plugin_class = self.plugins[plugin_type][plugin_name]
            return plugin_class()
        return None

    def list_plugins(self, plugin_type=None):
        """列出可用插件"""
        if plugin_type:
            return list(self.plugins.get(plugin_type, {}).keys())
        return {ptype: list(plugins.keys()) for ptype, plugins in self.plugins.items()}
```

**示例插件**: `plugins/html_report_generator.py`

```python
from plugin_manager import ReportGeneratorPlugin

class HTMLReportGenerator(ReportGeneratorPlugin):
    """HTML报告生成器插件"""

    def generate(self, results, config):
        """生成HTML格式报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{title}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ color: #2196F3; border-bottom: 2px solid #2196F3; }}
                .summary {{ background: #f5f5f5; padding: 20px; margin: 20px 0; }}
                .result {{ margin: 20px 0; padding: 15px; border-left: 4px solid #4CAF50; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{title}</h1>
                <p>生成时间: {timestamp}</p>
            </div>
            <div class="summary">
                <h2>分析概览</h2>
                <p>分析文件数: {file_count}</p>
            </div>
            {content}
        </body>
        </html>
        """

        # 生成内容
        content = ""
        for result in results:
            content += f"""
            <div class="result">
                <h3>{result.audio_file}</h3>
                <p>睡眠适用性得分: {result.sleep_suitability.overall_score:.1f}/100</p>
                <p>安全等级: {result.safety_assessment.overall_safety.value}</p>
            </div>
            """

        return html_template.format(
            title=config.get('title', '分析报告'),
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            file_count=len(results),
            content=content
        )

    def get_supported_formats(self):
        """返回支持的格式"""
        return ['html']
```

---

## 📋 执行检查清单

### 第1周检查清单 (P0任务)
- [ ] JSON序列化问题完全解决
- [ ] 所有numpy类型正确转换
- [ ] 现有功能100%兼容
- [ ] 回归测试全部通过
- [ ] 性能无明显影响

### 第2周检查清单 (P1任务)
- [ ] 新参数接口设计合理
- [ ] Markdown输出格式正确
- [ ] 自动命名功能正常
- [ ] 对比分析表格完整
- [ ] 用户体验明显提升

### 第3周检查清单 (P2任务)
- [ ] 配置文件格式规范
- [ ] 配置加载器稳定
- [ ] 模板引擎正常工作
- [ ] 热更新功能可用
- [ ] 错误处理完善

### 第4周检查清单 (P3任务)
- [ ] 插件接口设计清晰
- [ ] 插件管理器稳定
- [ ] 示例插件可用
- [ ] 插件文档完整
- [ ] 安全机制有效

---

**执行计划制定完成**，请按照优先级和时间线执行各项任务。建议每周进行进度回顾和风险评估，确保项目按计划顺利推进。
