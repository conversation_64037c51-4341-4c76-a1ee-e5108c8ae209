<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能睡眠音频评估系统 - 分析报告</title>
    <style>
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #2196F3;
            margin-bottom: 30px;
        }
        
        .report-header h1 {
            color: #2196F3;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .report-meta {
            color: #666;
            font-size: 0.9em;
        }
        
        .report-meta span {
            margin: 0 15px;
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        
        .section h2 {
            color: #2196F3;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary-card h3 {
            color: #2196F3;
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .ranking-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ranking-table th,
        .ranking-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .ranking-table th {
            background: #2196F3;
            color: white;
            font-weight: 600;
        }
        
        .ranking-table tr:hover {
            background: #f5f5f5;
        }
        
        .score-excellent { color: #4CAF50; font-weight: bold; }
        .score-good { color: #2196F3; font-weight: bold; }
        .score-fair { color: #FF9800; font-weight: bold; }
        .score-poor { color: #F44336; font-weight: bold; }
        
        .status-excellent { background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-good { background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-fair { background: #FF9800; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-poor { background: #F44336; color: white; padding: 4px 8px; border-radius: 4px; }
        
        .report-footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 30px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .report-header h1 {
                font-size: 2em;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
            
    </style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>智能睡眠音频评估系统 - 分析报告</h1>
            <div class="report-meta">
                <span class="timestamp">生成时间: 2025年06月26日 12:02:13</span>
                <span class="version">系统版本: 2.0</span>
            </div>
        </header>
        
        <main class="report-content">
            
        <div class="section">
            <h2>📊 分析摘要</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>1</h3>
                    <p>分析文件总数</p>
                </div>
                <div class="summary-card">
                    <h3>0</h3>
                    <p>优秀文件 (≥80分)</p>
                </div>
                <div class="summary-card">
                    <h3>0</h3>
                    <p>良好文件 (60-79分)</p>
                </div>
                <div class="summary-card">
                    <h3>19.3</h3>
                    <p>平均得分</p>
                </div>
            </div>
        </div>
        
            
        <div class="section">
            <h2>🏆 文件排名</h2>
            <table class="ranking-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>文件名</th>
                        <th>睡眠适用性得分</th>
                        <th>噪音类型</th>
                        <th>安全等级</th>
                        <th>推荐状态</th>
                    </tr>
                </thead>
                <tbody>
                    
            <tr>
                <td>1</td>
                <td><strong>waves.ogm</strong></td>
                <td class="score-poor">19.3/100</td>
                <td>复杂噪音</td>
                <td>警告</td>
                <td><span class="status-poor">❌ 不推荐</span></td>
            </tr>
            
                </tbody>
            </table>
        </div>
        
            
        <div class="section">
            <h2>📈 数据可视化</h2>
            <p><em>图表功能将在后续版本中提供</em></p>
        </div>
        
            
        <div class="section">
            <h2>📋 详细分析</h2>
            
            <div class="file-detail">
                <h3>🎵 waves.ogm</h3>
                <div class="detail-grid">
                    <div><strong>睡眠适用性得分:</strong> 19.3/100</div>
                    <div><strong>噪音类型:</strong> 复杂噪音</div>
                    <div><strong>安全等级:</strong> 警告</div>
                    <div><strong>效果预测:</strong> 5.8%</div>
                </div>
                <p><strong>总体推荐:</strong> ❌ 不推荐使用：效果差或存在风险</p>
            </div>
            
        </div>
        
        </main>
        
        <footer class="report-footer">
            <p>© 2025 智能睡眠音频评估系统 - 基于科学研究的睡眠音频分析</p>
        </footer>
    </div>
</body>
</html>