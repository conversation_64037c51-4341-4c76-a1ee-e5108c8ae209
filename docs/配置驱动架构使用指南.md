# 智能睡眠音频评估系统 - 配置驱动架构使用指南

## 📋 概述

智能睡眠音频评估系统v2.0采用配置驱动架构，支持YAML/JSON配置文件、Jinja2模板引擎、配置热更新等高级功能。本指南详细介绍如何使用和自定义系统配置。

## 🏗️ 系统架构

### 核心组件

1. **配置管理器** (`config_manager.py`)
   - 支持YAML和JSON格式配置文件
   - 配置验证和错误处理
   - 配置热更新（需要watchdog库）
   - 配置合并和继承

2. **模板引擎** (`template_engine.py`)
   - 支持Jinja2和内置简化模板引擎
   - 模板继承和组件复用
   - 多格式报告生成（文本、Markdown、JSON）

3. **配置驱动系统** (`config_driven_sleep_system.py`)
   - 基于配置的音频分析参数
   - 动态安全阈值设置
   - 可配置的评分权重和算法

## ⚙️ 配置文件结构

### 主配置文件 (`config/default.yaml` 或 `config/default.json`)

```yaml
# 系统基础配置
system:
  version: "2.0"
  debug_mode: false
  log_level: "INFO"
  supported_formats: ["wav", "mp3", "flac", "ogg", "ogm", "m4a", "aac"]

# 音频分析参数
analysis:
  spectral_analysis:
    frame_size: 4096
    hop_length: 2048
    window_type: "hann"
  
  bark_analysis:
    num_bands: 24
    freq_range: [20, 20000]

# 噪音类型配置
noise_types:
  pink:
    spectral_slope_range: [-1.2, -0.8]
    effectiveness: 0.82
    description: "1/f频谱的噪音，睡眠效果最佳"
  
  white:
    spectral_slope_range: [-0.2, 0.2]
    effectiveness: 0.33
    description: "平坦频谱的噪音"

# 安全阈值配置
safety_thresholds:
  adult:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    recommended_volume_range: [45, 60]
  
  infant:
    max_db: 50
    min_distance_cm: 200
    max_duration_hours: 1
    recommended_volume_range: [35, 45]

# 睡眠评估配置
sleep_evaluation:
  scoring_weights:
    stability_factor: 0.30
    tonal_factor: 0.30
    dynamic_factor: 0.25
    spectrum_factor: 0.15
  
  noise_type_factors:
    pink: 1.0
    brown: 0.8
    green: 0.7
    white: 0.4
```

### 配置文件优先级

1. `config/default.yaml` (或 `default.json`) - 基础配置
2. `config/*.yaml` (或 `*.json`) - 额外配置文件
3. 运行时设置 - 程序中动态设置的配置

## 🔧 配置管理

### 基本使用

```python
from config_manager import ConfigManager, get_config

# 创建配置管理器
config_manager = ConfigManager()

# 获取配置值
version = config_manager.get('system.version')
max_db = config_manager.get('safety_thresholds.adult.max_db')

# 设置配置值
config_manager.set('system.debug_mode', True)

# 便捷函数
debug_mode = get_config('system.debug_mode', False)
```

### 配置验证模式

```python
# 严格验证模式（生产环境）
config_manager = ConfigManager(strict_validation=True)

# 宽松验证模式（测试环境）
config_manager = ConfigManager(strict_validation=False)
```

### 配置热更新

```python
# 启用热更新（需要安装watchdog）
config_manager.enable_hot_reload()

# 添加配置变更回调
def on_config_change(event):
    print(f"配置已更新: {event.file_path}")

config_manager.add_change_callback(on_config_change)
```

## 📝 模板系统

### 模板文件结构

```
templates/
├── base_report.txt          # 基础文本报告模板
├── markdown_report.md       # Markdown报告模板
├── json_report.json         # JSON报告模板
└── components/              # 可复用组件
    ├── file_analysis.txt
    ├── user_group_recommendations.txt
    ├── top_recommendations.txt
    └── scientific_references.txt
```

### 模板语法

#### 变量替换
```
{{ variable_name }}
{{ nested.variable }}
```

#### 条件语句
```
{% if condition %}
内容
{% endif %}

{% if score > 80 %}
优秀评分！
{% endif %}
```

#### 循环语句
```
{% for item in items %}
- {{ item }}
{% endfor %}
```

#### 模板继承
```
{% extends "base_report.txt" %}

{% block content %}
自定义内容
{% endblock %}
```

#### 包含组件
```
{% include "components/file_analysis.txt" %}
```

### 模板使用示例

```python
from template_engine import TemplateManager

# 创建模板管理器
template_manager = TemplateManager()

# 准备数据
context = {
    'filename': 'test.wav',
    'score': 85,
    'noise_type': 'pink'
}

# 渲染模板
report = template_manager.render('base_report.txt', context)

# 创建自定义模板
custom_template = """
文件: {{ filename }}
得分: {{ score }}
{% if score > 80 %}
推荐使用！
{% endif %}
"""
template_manager.create_template('custom.txt', custom_template)
```

## 🎯 配置驱动系统使用

### 基本分析

```python
from config_driven_sleep_system import ConfigDrivenSleepAudioSystem

# 创建系统实例
system = ConfigDrivenSleepAudioSystem()

# 分析音频文件
report = system.analyze_audio_file('audio.wav')

# 生成不同格式的报告
text_report = system.generate_report(report, 'text')
markdown_report = system.generate_report(report, 'markdown')
json_report = system.generate_report(report, 'json')
```

### 用户群体特定分析

```python
# 为特定用户群体生成报告
adult_report = system.generate_report(report, 'text', 'adult')
infant_report = system.generate_report(report, 'text', 'infant')
elderly_report = system.generate_report(report, 'text', 'elderly')
insomnia_report = system.generate_report(report, 'text', 'insomnia')
```

### 便捷函数

```python
from config_driven_sleep_system import analyze_with_config

# 一键分析和报告生成
result = analyze_with_config('audio.wav', 'markdown', 'adult')
print(result)
```

## 🔄 配置自定义

### 添加新的噪音类型

```yaml
noise_types:
  custom_noise:
    spectral_slope_range: [-2.0, -1.5]
    effectiveness: 0.75
    description: "自定义噪音类型"
    experimental: true
```

### 自定义安全阈值

```yaml
safety_thresholds:
  custom_group:
    max_db: 55
    min_distance_cm: 40
    max_duration_hours: 6
    recommended_volume_range: [40, 55]
    special_considerations:
      - "特殊注意事项1"
      - "特殊注意事项2"
```

### 调整评分权重

```yaml
sleep_evaluation:
  scoring_weights:
    stability_factor: 0.35    # 增加稳定性权重
    tonal_factor: 0.25        # 减少音调性权重
    dynamic_factor: 0.25
    spectrum_factor: 0.15
```

## 🧪 测试和验证

### 运行单元测试

```bash
python3 test_config_system_unit.py
```

### 运行集成测试

```bash
python3 test_config_driven_system.py
```

### 配置验证

```python
from config_manager import ConfigManager

try:
    config_manager = ConfigManager(strict_validation=True)
    print("配置验证通过")
except ConfigValidationError as e:
    print(f"配置验证失败: {e}")
```

## 🚀 性能优化

### 缓存配置

```yaml
performance:
  cache:
    enabled: true
    max_size_mb: 256
    ttl_seconds: 3600
```

### 并行处理

```yaml
performance:
  parallel_processing:
    enabled: true
    max_workers: 4
    chunk_size: 1
```

## 🔍 故障排除

### 常见问题

1. **配置文件格式错误**
   - 检查YAML/JSON语法
   - 使用在线验证工具

2. **模板渲染失败**
   - 检查变量名是否正确
   - 验证模板语法

3. **热更新不工作**
   - 确保安装了watchdog库
   - 检查文件权限

4. **配置验证失败**
   - 使用宽松模式进行调试
   - 检查必需的配置项

### 调试模式

```yaml
system:
  debug_mode: true
  log_level: "DEBUG"

logging:
  level: "DEBUG"
  console_output: true
```

## 📚 最佳实践

1. **配置管理**
   - 使用版本控制管理配置文件
   - 为不同环境创建不同的配置文件
   - 定期备份重要配置

2. **模板设计**
   - 使用组件化设计提高复用性
   - 保持模板简洁易读
   - 添加适当的注释

3. **性能优化**
   - 启用配置缓存
   - 合理设置并行处理参数
   - 定期清理日志文件

4. **安全考虑**
   - 验证用户输入的配置值
   - 限制配置文件的访问权限
   - 定期审查配置变更

## 🔗 相关文档

- [系统架构文档](系统架构文档.md)
- [API参考文档](API参考文档.md)
- [部署指南](部署指南.md)
- [故障排除指南](故障排除指南.md)
