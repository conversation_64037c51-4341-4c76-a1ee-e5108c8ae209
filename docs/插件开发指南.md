# 🔌 智能睡眠音频评估系统 - 插件开发指南

## 📋 目录

1. [概述](#概述)
2. [插件系统架构](#插件系统架构)
3. [开发环境准备](#开发环境准备)
4. [创建报告生成器插件](#创建报告生成器插件)
5. [创建分析插件](#创建分析插件)
6. [插件配置和验证](#插件配置和验证)
7. [测试和调试](#测试和调试)
8. [部署和分发](#部署和分发)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

---

## 概述

智能睡眠音频评估系统的插件系统允许开发者扩展系统功能，主要支持两种类型的插件：

- **报告生成器插件**: 生成不同格式的分析报告（HTML、PDF、Excel等）
- **分析插件**: 提供额外的音频分析功能

### 插件系统特性

- ✅ **动态加载**: 运行时自动发现和加载插件
- ✅ **类型安全**: 基于抽象基类的强类型接口
- ✅ **配置验证**: 插件配置参数验证机制
- ✅ **错误处理**: 完善的错误处理和降级机制
- ✅ **生命周期管理**: 插件初始化、清理和资源管理
- ✅ **依赖检查**: 自动检查插件所需的依赖库

---

## 插件系统架构

```
plugin_system.py
├── PluginInterface (基础接口)
├── ReportGeneratorPlugin (报告生成器基类)
├── AnalysisPlugin (分析插件基类)
├── PluginRegistry (插件注册表)
└── PluginManager (插件管理器)

plugins/
├── __init__.py
├── html_report_generator.py
├── pdf_report_generator.py
├── csv_report_generator.py
└── your_custom_plugin.py
```

### 核心组件

1. **PluginInterface**: 所有插件的基础接口
2. **ReportGeneratorPlugin**: 报告生成器插件基类
3. **AnalysisPlugin**: 分析插件基类
4. **PluginRegistry**: 管理插件注册和发现
5. **PluginManager**: 处理插件加载、实例化和生命周期

---

## 开发环境准备

### 1. 环境要求

- Python 3.8+
- 智能睡眠音频评估系统核心库

### 2. 目录结构

```bash
your_project/
├── plugin_system.py          # 插件系统核心
├── plugins/                  # 插件目录
│   ├── __init__.py
│   └── your_plugin.py       # 你的插件文件
├── run_sleep_audio_analysis.py  # 主程序
└── docs/                    # 文档
```

### 3. 依赖安装

根据插件类型安装相应依赖：

```bash
# HTML报告插件（无额外依赖）
# PDF报告插件
pip install reportlab

# Excel报告插件
pip install openpyxl

# 图表功能
pip install matplotlib seaborn
```

---

## 创建报告生成器插件

### 1. 基本结构

```python
# plugins/my_report_generator.py
import sys
from pathlib import Path

# 导入插件基类
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin

class MyReportGenerator(ReportGeneratorPlugin):
    """自定义报告生成器插件"""
    
    def get_plugin_info(self):
        """返回插件信息"""
        return {
            'name': 'MyReportGenerator',
            'version': '1.0',
            'description': '我的自定义报告生成器',
            'author': '你的名字',
            'supported_formats': ['myformat'],
            'dependencies': []  # 所需依赖库列表
        }
    
    def validate_config(self, config):
        """验证配置参数"""
        # 在这里验证配置参数
        return True
    
    def get_supported_formats(self):
        """返回支持的输出格式"""
        return ['myformat']
    
    def generate_report(self, results, config):
        """生成报告的核心方法"""
        try:
            # 在这里实现报告生成逻辑
            report_content = self._create_report_content(results, config)
            
            # 如果配置了输出路径，保存到文件
            output_path = config.get('output_path')
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                return f"报告已保存到: {output_path}"
            else:
                return report_content
                
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            return f"错误: {str(e)}"
    
    def _create_report_content(self, results, config):
        """创建报告内容的辅助方法"""
        lines = []
        lines.append("=== 我的自定义报告 ===")
        lines.append(f"分析文件数: {len(results)}")
        
        for i, result in enumerate(results, 1):
            filename = Path(result.audio_file).name
            score = result.sleep_suitability.overall_score
            lines.append(f"{i}. {filename}: {score:.1f}/100")
        
        return "\n".join(lines)
```

### 2. 高级功能示例

```python
class AdvancedReportGenerator(ReportGeneratorPlugin):
    """高级报告生成器示例"""
    
    def __init__(self, config=None):
        super().__init__(config)
        # 检查依赖
        self.check_dependencies()
    
    def check_dependencies(self):
        """检查依赖库"""
        try:
            import matplotlib
            self.matplotlib_available = True
        except ImportError:
            self.matplotlib_available = False
            self.logger.warning("matplotlib不可用，图表功能将被禁用")
    
    def get_plugin_info(self):
        return {
            'name': 'AdvancedReportGenerator',
            'version': '2.0',
            'description': '支持图表的高级报告生成器',
            'dependencies': ['matplotlib'],
            'available': self.matplotlib_available
        }
    
    def validate_config(self, config):
        """高级配置验证"""
        required_params = ['title', 'output_format']
        for param in required_params:
            if param not in config:
                self.logger.error(f"缺少必需参数: {param}")
                return False
        
        # 验证输出格式
        valid_formats = self.get_supported_formats()
        if config['output_format'] not in valid_formats:
            self.logger.error(f"不支持的输出格式: {config['output_format']}")
            return False
        
        return True
    
    def generate_report(self, results, config):
        """生成高级报告"""
        if not self.validate_config(config):
            return "配置验证失败"
        
        # 根据配置生成不同类型的报告
        output_format = config['output_format']
        
        if output_format == 'html_with_charts':
            return self._generate_html_with_charts(results, config)
        elif output_format == 'summary':
            return self._generate_summary_report(results, config)
        else:
            return self._generate_basic_report(results, config)
```

---

## 创建分析插件

### 1. 基本结构

```python
# plugins/my_analysis_plugin.py
from plugin_system import AnalysisPlugin

class MyAnalysisPlugin(AnalysisPlugin):
    """自定义分析插件"""
    
    def get_plugin_info(self):
        return {
            'name': 'MyAnalysisPlugin',
            'version': '1.0',
            'description': '我的自定义音频分析插件',
            'analysis_type': 'custom_analysis'
        }
    
    def validate_config(self, config):
        """验证分析配置"""
        return True
    
    def get_analysis_type(self):
        """返回分析类型"""
        return 'custom_analysis'
    
    def analyze(self, audio_data, config):
        """执行音频分析"""
        try:
            # 在这里实现你的分析逻辑
            analysis_result = {
                'custom_metric': self._calculate_custom_metric(audio_data),
                'analysis_timestamp': datetime.now().isoformat(),
                'config_used': config
            }
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析失败: {e}")
            return {'error': str(e)}
    
    def _calculate_custom_metric(self, audio_data):
        """计算自定义指标"""
        # 实现你的分析算法
        return 42.0  # 示例返回值
```

---

## 插件配置和验证

### 1. 配置文件格式

创建 `plugin_config.json`:

```json
{
    "MyReportGenerator": {
        "title": "自定义分析报告",
        "output_format": "html_with_charts",
        "include_charts": true,
        "chart_style": "modern",
        "color_scheme": "blue"
    },
    "PDFReportGenerator": {
        "page_size": "A4",
        "font_size": 12,
        "include_logo": true
    }
}
```

### 2. 配置验证最佳实践

```python
def validate_config(self, config):
    """配置验证示例"""
    # 1. 检查必需参数
    required_params = ['title', 'output_format']
    for param in required_params:
        if param not in config:
            self.logger.error(f"缺少必需参数: {param}")
            return False
    
    # 2. 验证参数类型
    if not isinstance(config.get('include_charts', True), bool):
        self.logger.error("include_charts 必须是布尔值")
        return False
    
    # 3. 验证参数值范围
    font_size = config.get('font_size', 12)
    if not (8 <= font_size <= 24):
        self.logger.error("font_size 必须在 8-24 之间")
        return False
    
    # 4. 验证依赖
    if config.get('include_charts') and not self.matplotlib_available:
        self.logger.error("图表功能需要 matplotlib 库")
        return False
    
    return True
```

---

## 测试和调试

### 1. 单元测试

```python
# test_my_plugin.py
import unittest
from plugins.my_report_generator import MyReportGenerator

class TestMyReportGenerator(unittest.TestCase):
    
    def setUp(self):
        self.plugin = MyReportGenerator()
    
    def test_plugin_info(self):
        """测试插件信息"""
        info = self.plugin.get_plugin_info()
        self.assertEqual(info['name'], 'MyReportGenerator')
        self.assertEqual(info['version'], '1.0')
    
    def test_config_validation(self):
        """测试配置验证"""
        valid_config = {'title': 'Test Report'}
        self.assertTrue(self.plugin.validate_config(valid_config))
    
    def test_report_generation(self):
        """测试报告生成"""
        mock_results = [MockResult()]
        config = {'title': 'Test Report'}
        
        report = self.plugin.generate_report(mock_results, config)
        self.assertIn('Test Report', report)

if __name__ == '__main__':
    unittest.main()
```

### 2. 调试技巧

```python
# 在插件中添加调试日志
def generate_report(self, results, config):
    self.logger.debug(f"开始生成报告，结果数量: {len(results)}")
    self.logger.debug(f"配置参数: {config}")
    
    try:
        # 报告生成逻辑
        report = self._create_report(results, config)
        self.logger.info("报告生成成功")
        return report
    except Exception as e:
        self.logger.error(f"报告生成失败: {e}", exc_info=True)
        raise
```

### 3. 集成测试

```bash
# 测试插件加载
python -c "
from plugin_system import get_plugin_manager
manager = get_plugin_manager()
print(manager.list_available_plugins())
"

# 测试报告生成
python run_sleep_audio_analysis.py noisekun/waves.ogm --plugin MyReportGenerator --plugin-config plugin_config.json

---

## 部署和分发

### 1. 插件打包

创建插件包结构：

```
my_plugin_package/
├── setup.py
├── README.md
├── requirements.txt
└── my_plugin/
    ├── __init__.py
    └── my_report_generator.py
```

`setup.py` 示例：

```python
from setuptools import setup, find_packages

setup(
    name="my-sleep-audio-plugin",
    version="1.0.0",
    description="自定义睡眠音频分析报告插件",
    author="你的名字",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "reportlab>=3.5.0",  # 如果需要
    ],
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
    ],
)
```

### 2. 安装插件

```bash
# 从源码安装
pip install -e .

# 从PyPI安装
pip install my-sleep-audio-plugin

# 复制插件文件到plugins目录
cp my_plugin/my_report_generator.py /path/to/sleep_system/plugins/
```

### 3. 插件发现机制

系统会自动扫描以下位置的插件：

1. `plugins/` 目录（相对于主程序）
2. 用户指定的插件目录
3. 已安装的Python包中的插件

---

## 最佳实践

### 1. 代码规范

```python
class WellDesignedPlugin(ReportGeneratorPlugin):
    """设计良好的插件示例"""

    def __init__(self, config=None):
        super().__init__(config)
        self._validate_dependencies()
        self._setup_logging()

    def _validate_dependencies(self):
        """验证依赖库"""
        self.dependencies_ok = True
        for dep in self.get_required_dependencies():
            try:
                __import__(dep)
            except ImportError:
                self.logger.error(f"缺少依赖库: {dep}")
                self.dependencies_ok = False

    def _setup_logging(self):
        """设置日志"""
        self.logger.setLevel(logging.INFO)

    def get_required_dependencies(self):
        """返回所需依赖"""
        return ['matplotlib', 'pandas']

    def validate_config(self, config):
        """验证配置"""
        if not self.dependencies_ok:
            return False

        # 详细的配置验证逻辑
        return self._validate_config_schema(config)

    def _validate_config_schema(self, config):
        """验证配置模式"""
        schema = {
            'title': str,
            'include_charts': bool,
            'output_format': str
        }

        for key, expected_type in schema.items():
            if key in config and not isinstance(config[key], expected_type):
                self.logger.error(f"配置参数 {key} 类型错误，期望 {expected_type}")
                return False

        return True
```

### 2. 错误处理

```python
def generate_report(self, results, config):
    """带有完善错误处理的报告生成"""
    try:
        # 验证输入
        if not results:
            raise ValueError("没有分析结果")

        if not self.validate_config(config):
            raise ValueError("配置验证失败")

        # 生成报告
        report = self._create_report_content(results, config)

        # 保存文件（如果需要）
        output_path = config.get('output_path')
        if output_path:
            self._save_report(report, output_path)
            return f"报告已保存到: {output_path}"

        return report

    except ValueError as e:
        self.logger.error(f"输入验证错误: {e}")
        return f"错误: {e}"
    except IOError as e:
        self.logger.error(f"文件操作错误: {e}")
        return f"文件错误: {e}"
    except Exception as e:
        self.logger.error(f"未知错误: {e}", exc_info=True)
        return f"生成报告时发生错误: {e}"

def _save_report(self, content, output_path):
    """安全地保存报告"""
    try:
        # 确保目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        # 保存文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)

    except Exception as e:
        raise IOError(f"无法保存文件到 {output_path}: {e}")
```

### 3. 性能优化

```python
class OptimizedPlugin(ReportGeneratorPlugin):
    """性能优化的插件示例"""

    def __init__(self, config=None):
        super().__init__(config)
        self._template_cache = {}
        self._result_cache = {}

    def generate_report(self, results, config):
        """带缓存的报告生成"""
        # 生成缓存键
        cache_key = self._generate_cache_key(results, config)

        # 检查缓存
        if cache_key in self._result_cache:
            self.logger.debug("使用缓存的报告")
            return self._result_cache[cache_key]

        # 生成新报告
        report = self._create_report(results, config)

        # 缓存结果
        self._result_cache[cache_key] = report

        return report

    def _generate_cache_key(self, results, config):
        """生成缓存键"""
        import hashlib

        # 基于结果和配置生成哈希
        content = f"{len(results)}_{hash(str(config))}"
        return hashlib.md5(content.encode()).hexdigest()
```

---

## 常见问题

### Q1: 插件无法加载怎么办？

**A**: 检查以下几点：

1. 插件文件是否在正确的目录中
2. 插件类是否继承了正确的基类
3. 是否有语法错误或导入错误
4. 检查日志输出获取详细错误信息

```bash
# 启用调试日志
python run_sleep_audio_analysis.py --list-plugins --verbose
```

### Q2: 如何处理插件依赖？

**A**: 在插件中检查依赖并提供友好的错误信息：

```python
def __init__(self, config=None):
    super().__init__(config)
    try:
        import required_library
        self.library_available = True
    except ImportError:
        self.library_available = False
        self.logger.warning("请安装 required_library: pip install required_library")

def validate_config(self, config):
    if not self.library_available:
        return False
    return True
```

### Q3: 插件配置参数如何传递？

**A**: 通过命令行参数或配置文件：

```bash
# 使用配置文件
python run_sleep_audio_analysis.py input.wav --plugin MyPlugin --plugin-config config.json

# 配置文件内容
{
    "MyPlugin": {
        "param1": "value1",
        "param2": true
    }
}
```

### Q4: 如何调试插件？

**A**: 使用日志和异常处理：

```python
def generate_report(self, results, config):
    self.logger.debug(f"插件开始执行，参数: {config}")

    try:
        # 插件逻辑
        result = self._do_work(results, config)
        self.logger.info("插件执行成功")
        return result
    except Exception as e:
        self.logger.error(f"插件执行失败: {e}", exc_info=True)
        raise
```

### Q5: 插件性能优化建议？

**A**:

1. **使用缓存**: 缓存计算结果和模板
2. **延迟加载**: 只在需要时导入重型库
3. **批量处理**: 一次处理多个结果
4. **内存管理**: 及时释放大对象

```python
# 延迟导入示例
def _get_heavy_library(self):
    if not hasattr(self, '_heavy_lib'):
        import heavy_library
        self._heavy_lib = heavy_library
    return self._heavy_lib
```

---

## 📚 参考资源

- [插件系统API文档](./插件系统API文档.md)
- [示例插件代码](../plugins/)
- [测试用例](../test_plugin_system.py)
- [主程序集成](../run_sleep_audio_analysis.py)

---

**🎉 恭喜！你现在已经掌握了插件开发的基础知识。开始创建你的第一个插件吧！**
```
