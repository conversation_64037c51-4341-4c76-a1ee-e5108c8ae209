# 🔌 插件系统API文档

## 📋 目录

1. [核心接口](#核心接口)
2. [插件基类](#插件基类)
3. [插件管理器](#插件管理器)
4. [插件注册表](#插件注册表)
5. [异常类](#异常类)
6. [工具函数](#工具函数)

---

## 核心接口

### PluginInterface

所有插件的基础接口类。

```python
class PluginInterface(ABC):
    """插件接口基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化插件
        
        Args:
            config: 插件配置参数
        """
```

#### 抽象方法

##### get_plugin_info()

```python
@abstractmethod
def get_plugin_info(self) -> Dict[str, Any]:
    """
    获取插件信息
    
    Returns:
        包含插件名称、版本、描述等信息的字典
        
    Required keys:
        - name: 插件名称
        - version: 插件版本
        - description: 插件描述
        
    Optional keys:
        - author: 作者
        - supported_formats: 支持的格式列表
        - dependencies: 依赖库列表
        - available: 插件是否可用
    """
```

##### validate_config()

```python
@abstractmethod
def validate_config(self, config: Dict[str, Any]) -> bool:
    """
    验证配置的有效性
    
    Args:
        config: 配置参数字典
        
    Returns:
        配置是否有效
    """
```

#### 实例方法

##### initialize()

```python
def initialize(self) -> bool:
    """
    初始化插件
    
    Returns:
        初始化是否成功
    """
```

##### cleanup()

```python
def cleanup(self):
    """清理插件资源"""
```

#### 属性

##### is_initialized

```python
@property
def is_initialized(self) -> bool:
    """检查插件是否已初始化"""
```

---

## 插件基类

### ReportGeneratorPlugin

报告生成器插件基类，继承自 `PluginInterface`。

```python
class ReportGeneratorPlugin(PluginInterface):
    """报告生成器插件基类"""
```

#### 抽象方法

##### generate_report()

```python
@abstractmethod
def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
    """
    生成报告的核心方法
    
    Args:
        results: 分析结果列表，每个元素包含音频分析结果
        config: 生成配置字典
        
    Returns:
        生成的报告内容或状态信息
        
    Config parameters:
        - title: 报告标题
        - output_path: 输出文件路径（可选）
        - template_style: 模板样式
        - include_charts: 是否包含图表
        - user_group: 目标用户群体
        - detailed: 是否生成详细报告
    """
```

##### get_supported_formats()

```python
@abstractmethod
def get_supported_formats(self) -> List[str]:
    """
    返回支持的输出格式列表
    
    Returns:
        支持的格式列表，如 ['html', 'pdf']
    """
```

#### 实例方法

##### get_file_extension()

```python
def get_file_extension(self, format_name: str) -> str:
    """
    获取指定格式的文件扩展名
    
    Args:
        format_name: 格式名称
        
    Returns:
        文件扩展名，如 '.html'
    """
```

### AnalysisPlugin

分析插件基类，继承自 `PluginInterface`。

```python
class AnalysisPlugin(PluginInterface):
    """分析插件基类"""
```

#### 抽象方法

##### analyze()

```python
@abstractmethod
def analyze(self, audio_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行分析的核心方法
    
    Args:
        audio_data: 音频数据
        config: 分析配置
        
    Returns:
        分析结果字典
    """
```

##### get_analysis_type()

```python
@abstractmethod
def get_analysis_type(self) -> str:
    """
    返回分析类型
    
    Returns:
        分析类型标识符
    """
```

#### 实例方法

##### get_required_dependencies()

```python
def get_required_dependencies(self) -> List[str]:
    """
    获取插件所需的依赖库
    
    Returns:
        依赖库列表
    """
```

---

## 插件管理器

### PluginManager

插件管理器类，负责插件的加载、管理和生命周期。

```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self, plugin_dir: str = "plugins", 
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化插件管理器
        
        Args:
            plugin_dir: 插件目录路径
            config: 插件系统配置
        """
```

#### 主要方法

##### load_all_plugins()

```python
def load_all_plugins(self) -> Dict[str, bool]:
    """
    加载所有插件
    
    Returns:
        加载结果字典，键为插件文件名，值为加载是否成功
    """
```

##### load_plugin_from_file()

```python
def load_plugin_from_file(self, plugin_file: Path) -> bool:
    """
    从文件加载插件
    
    Args:
        plugin_file: 插件文件路径
        
    Returns:
        加载是否成功
    """
```

##### create_plugin_instance()

```python
def create_plugin_instance(self, plugin_type: str, plugin_name: str, 
                          config: Optional[Dict[str, Any]] = None) -> Optional[PluginInterface]:
    """
    创建插件实例
    
    Args:
        plugin_type: 插件类型 ('report_generators' 或 'analyzers')
        plugin_name: 插件名称
        config: 插件配置
        
    Returns:
        插件实例或None
    """
```

##### get_plugin_instance()

```python
def get_plugin_instance(self, plugin_type: str, plugin_name: str) -> Optional[PluginInterface]:
    """
    获取插件实例（如果不存在则创建）
    
    Args:
        plugin_type: 插件类型
        plugin_name: 插件名称
        
    Returns:
        插件实例或None
    """
```

##### list_available_plugins()

```python
def list_available_plugins(self) -> Dict[str, List[Dict[str, Any]]]:
    """
    列出所有可用插件及其信息
    
    Returns:
        插件信息字典，格式为:
        {
            'report_generators': [
                {
                    'name': 'HTMLReportGenerator',
                    'version': '1.0',
                    'description': '...',
                    'type': 'report_generators'
                }
            ],
            'analyzers': [...]
        }
    """
```

##### cleanup_all_plugins()

```python
def cleanup_all_plugins(self):
    """清理所有插件实例"""
```

##### reload_plugins()

```python
def reload_plugins(self) -> Dict[str, bool]:
    """
    重新加载所有插件
    
    Returns:
        重新加载结果
    """
```

---

## 插件注册表

### PluginRegistry

插件注册表类，管理插件的注册和查找。

```python
class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        """初始化插件注册表"""
```

#### 主要方法

##### register_plugin()

```python
def register_plugin(self, plugin_class: Type[PluginInterface], plugin_type: str) -> bool:
    """
    注册插件
    
    Args:
        plugin_class: 插件类
        plugin_type: 插件类型 ('report_generators' 或 'analyzers')
        
    Returns:
        注册是否成功
    """
```

##### get_plugin_class()

```python
def get_plugin_class(self, plugin_type: str, plugin_name: str) -> Optional[Type[PluginInterface]]:
    """
    获取插件类
    
    Args:
        plugin_type: 插件类型
        plugin_name: 插件名称
        
    Returns:
        插件类或None
    """
```

##### list_plugins()

```python
def list_plugins(self, plugin_type: Optional[str] = None) -> Dict[str, List[str]]:
    """
    列出可用插件
    
    Args:
        plugin_type: 插件类型，None表示列出所有类型
        
    Returns:
        插件列表字典
    """
```

##### unregister_plugin()

```python
def unregister_plugin(self, plugin_type: str, plugin_name: str) -> bool:
    """
    注销插件
    
    Args:
        plugin_type: 插件类型
        plugin_name: 插件名称
        
    Returns:
        注销是否成功
    """
```

---

## 异常类

### PluginError

```python
class PluginError(Exception):
    """插件系统异常基类"""
```

### PluginLoadError

```python
class PluginLoadError(PluginError):
    """插件加载异常"""
```

### PluginValidationError

```python
class PluginValidationError(PluginError):
    """插件验证异常"""
```

---

## 工具函数

### get_plugin_manager()

```python
def get_plugin_manager(plugin_dir: str = "plugins", 
                      config: Optional[Dict[str, Any]] = None) -> PluginManager:
    """
    获取全局插件管理器实例
    
    Args:
        plugin_dir: 插件目录
        config: 配置参数
        
    Returns:
        插件管理器实例
    """
```

---

## 使用示例

### 基本使用

```python
from plugin_system import get_plugin_manager

# 获取插件管理器
manager = get_plugin_manager()

# 列出可用插件
plugins = manager.list_available_plugins()
print(plugins)

# 获取插件实例
html_generator = manager.get_plugin_instance('report_generators', 'HTMLReportGenerator')

# 生成报告
if html_generator:
    config = {
        'title': '测试报告',
        'output_path': 'test_report.html'
    }
    result = html_generator.generate_report(analysis_results, config)
    print(result)
```

### 错误处理

```python
try:
    plugin_instance = manager.create_plugin_instance(
        'report_generators', 
        'NonExistentPlugin'
    )
    if plugin_instance is None:
        print("插件不存在或创建失败")
        
except PluginLoadError as e:
    print(f"插件加载错误: {e}")
except PluginValidationError as e:
    print(f"插件配置验证错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

### 配置管理

```python
# 插件配置示例
plugin_config = {
    'HTMLReportGenerator': {
        'template_style': 'modern',
        'include_charts': True,
        'color_scheme': 'blue'
    },
    'PDFReportGenerator': {
        'page_size': 'A4',
        'font_size': 12
    }
}

# 使用配置创建插件实例
html_plugin = manager.create_plugin_instance(
    'report_generators', 
    'HTMLReportGenerator',
    plugin_config['HTMLReportGenerator']
)
```
