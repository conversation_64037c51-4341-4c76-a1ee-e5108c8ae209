# 配置文件示例和最佳实践

## 📋 基础配置示例

### 生产环境配置 (`config/production.yaml`)

```yaml
# 生产环境配置
system:
  version: "2.0"
  debug_mode: false
  log_level: "INFO"
  max_file_size_mb: 100
  supported_formats: ["wav", "mp3", "flac", "ogg", "ogm", "m4a", "aac"]

# 音频分析参数（优化后的生产设置）
analysis:
  audio_processing:
    default_sample_rate: null
    mono_conversion: true
    normalization: true
    
  spectral_analysis:
    frame_size: 4096
    hop_length: 2048
    overlap_ratio: 0.5
    window_type: "hann"
    
  bark_analysis:
    num_bands: 24
    freq_range: [20, 20000]

# 噪音类型配置（基于科学研究）
noise_types:
  white:
    spectral_slope_range: [-0.2, 0.2]
    effectiveness: 0.33
    description: "平坦频谱的噪音"
    
  pink:
    spectral_slope_range: [-1.2, -0.8]
    effectiveness: 0.82
    description: "1/f频谱的噪音，睡眠效果最佳"
    
  brown:
    spectral_slope_range: [-2.2, -1.8]
    effectiveness: 0.65
    description: "低频丰富的噪音"
    
  green:
    mid_freq_range: [400, 800]
    dominance_threshold: 1.5
    effectiveness: 0.55
    experimental: true
    description: "中频增强的噪音"

# 严格的安全阈值（生产环境）
safety_thresholds:
  adult:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    recommended_volume_range: [45, 60]
    
  infant:
    max_db: 50
    min_distance_cm: 200
    max_duration_hours: 1
    recommended_volume_range: [35, 45]
    special_considerations:
      - "仅入睡阶段使用"
      - "严格限制音量"
      - "保持安全距离"
    
  elderly:
    max_db: 55
    min_distance_cm: 50
    max_duration_hours: 6
    recommended_volume_range: [40, 55]
    
  insomnia:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    recommended_volume_range: [45, 58]

# 性能优化配置
performance:
  cache:
    enabled: true
    max_size_mb: 256
    ttl_seconds: 3600
    
  parallel_processing:
    enabled: true
    max_workers: 4
    chunk_size: 1

# 热更新配置（生产环境谨慎启用）
hot_reload:
  enabled: false
  watch_files: ["config/*.yaml"]
  reload_delay_seconds: 1
  backup_on_reload: true

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/sleep_audio_system.log"
  max_file_size_mb: 10
  backup_count: 5
  console_output: false
```

### 开发环境配置 (`config/development.yaml`)

```yaml
# 开发环境配置
system:
  version: "2.0-dev"
  debug_mode: true
  log_level: "DEBUG"

# 宽松的安全阈值（开发测试用）
safety_thresholds:
  adult:
    max_db: 70  # 开发环境允许更高音量
    min_distance_cm: 20
    max_duration_hours: 12
    
  test_group:  # 开发专用测试群体
    max_db: 80
    min_distance_cm: 10
    max_duration_hours: 24
    recommended_volume_range: [30, 80]

# 启用热更新（开发环境）
hot_reload:
  enabled: true
  watch_files: ["config/*.yaml", "templates/*.j2", "templates/*.txt"]
  reload_delay_seconds: 0.5

# 详细日志配置
logging:
  level: "DEBUG"
  console_output: true
  file_path: "logs/dev_sleep_audio_system.log"

# 开发工具配置
development:
  enable_profiling: true
  save_intermediate_results: true
  test_data_path: "test_data/"
```

### 测试环境配置 (`config/testing.yaml`)

```yaml
# 测试环境配置
system:
  version: "2.0-test"
  debug_mode: false
  log_level: "WARNING"

# 简化的噪音类型（测试用）
noise_types:
  pink:
    effectiveness: 0.82
  white:
    effectiveness: 0.33

# 最小安全阈值配置
safety_thresholds:
  adult:
    max_db: 60
    min_distance_cm: 30

# 禁用性能优化（测试一致性）
performance:
  cache:
    enabled: false
  parallel_processing:
    enabled: false

# 测试专用配置
testing:
  mock_audio_analysis: false
  save_test_reports: true
  test_output_dir: "test_outputs/"
```

## 🎯 专业应用配置示例

### 医疗机构配置 (`config/medical.yaml`)

```yaml
# 医疗机构专用配置
system:
  version: "2.0-medical"
  certification: "FDA-cleared"

# 严格的医疗级安全阈值
safety_thresholds:
  adult:
    max_db: 55  # 更保守的音量限制
    min_distance_cm: 50
    max_duration_hours: 6
    
  infant:
    max_db: 45  # 极其严格的婴儿保护
    min_distance_cm: 300
    max_duration_hours: 0.5
    medical_supervision_required: true
    
  elderly:
    max_db: 50
    min_distance_cm: 60
    hearing_assessment_required: true
    
  patient_with_hearing_loss:
    max_db: 40
    min_distance_cm: 100
    audiologist_approval_required: true

# 医疗级质量阈值
sleep_evaluation:
  quality_thresholds:
    max_dynamic_range_db: 5  # 更严格的动态范围
    max_tonal_ratio: 3
    min_stability_percent: 98
    max_disruption_risk: 0.1

# 医疗记录和审计
medical:
  enable_audit_log: true
  patient_data_encryption: true
  compliance_mode: "HIPAA"
  session_tracking: true
```

### 研究机构配置 (`config/research.yaml`)

```yaml
# 研究机构配置
system:
  version: "2.0-research"
  research_mode: true

# 扩展的噪音类型（研究用）
noise_types:
  white:
    effectiveness: 0.33
    research_notes: "Control group baseline"
    
  pink:
    effectiveness: 0.82
    research_notes: "Primary research target"
    
  brown:
    effectiveness: 0.65
    research_notes: "Low frequency preference study"
    
  green:
    effectiveness: 0.55
    experimental: true
    research_notes: "Novel mid-frequency concentration"
    
  violet:  # 研究专用
    spectral_slope_range: [1.5, 2.0]
    effectiveness: 0.25
    experimental: true
    research_notes: "High frequency noise study"

# 研究数据收集
research:
  collect_detailed_metrics: true
  save_raw_audio_features: true
  enable_statistical_analysis: true
  participant_anonymization: true
  
  study_parameters:
    control_group_size: 50
    treatment_group_size: 50
    study_duration_weeks: 12
    
  data_export:
    formats: ["csv", "json", "matlab"]
    include_metadata: true
    statistical_summary: true
```

## 🏠 家庭用户配置示例

### 家庭基础配置 (`config/home.yaml`)

```yaml
# 家庭用户友好配置
system:
  version: "2.0-home"
  user_friendly_mode: true

# 平衡的安全设置
safety_thresholds:
  adult:
    max_db: 60
    min_distance_cm: 30
    max_duration_hours: 8
    
  child:  # 儿童群体
    max_db: 55
    min_distance_cm: 100
    max_duration_hours: 2
    parental_supervision: true
    
  teenager:
    max_db: 58
    min_distance_cm: 40
    max_duration_hours: 6

# 简化的报告格式
report_generation:
  default_format: "text"
  templates:
    detailed_analysis: false  # 简化技术细节
    scientific_references: false
    safety_warnings: true
    personalized_recommendations: true
    
  content_options:
    include_technical_details: false
    include_usage_guidelines: true
    max_recommendations_per_group: 3

# 家庭友好的日志
logging:
  level: "WARNING"  # 只记录重要信息
  console_output: false
  file_path: "sleep_audio.log"
```

### 智能家居集成配置 (`config/smart_home.yaml`)

```yaml
# 智能家居集成配置
system:
  version: "2.0-smart"
  integration_mode: true

# 自动化配置
automation:
  bedtime_routine:
    enabled: true
    trigger_time: "22:00"
    auto_volume_adjustment: true
    
  wake_up_routine:
    enabled: true
    fade_out_duration_minutes: 30
    
  presence_detection:
    enabled: true
    adjust_for_multiple_users: true

# 设备集成
devices:
  smart_speakers:
    - type: "alexa"
      device_id: "echo_bedroom"
      max_volume: 40
      
    - type: "google_home"
      device_id: "nest_living_room"
      max_volume: 35
      
  sleep_trackers:
    - type: "fitbit"
      integration_enabled: true
      
    - type: "apple_watch"
      integration_enabled: true

# 环境适应
environmental:
  noise_floor_adaptation: true
  time_of_day_adjustment: true
  seasonal_preferences: true
```

## 🔧 自定义配置模板

### 创建自定义用户群体

```yaml
# 自定义用户群体示例
safety_thresholds:
  shift_worker:  # 轮班工作者
    max_db: 58
    min_distance_cm: 35
    max_duration_hours: 10
    recommended_volume_range: [42, 58]
    special_considerations:
      - "适应不规律睡眠时间"
      - "可能需要更强的噪音屏蔽"
      
  light_sleeper:  # 浅睡眠者
    max_db: 50
    min_distance_cm: 50
    max_duration_hours: 8
    recommended_volume_range: [35, 50]
    special_considerations:
      - "避免突发声音"
      - "优先选择稳定的噪音类型"
      
  tinnitus_patient:  # 耳鸣患者
    max_db: 45
    min_distance_cm: 60
    max_duration_hours: 6
    recommended_volume_range: [30, 45]
    special_considerations:
      - "避免高频成分"
      - "医生指导下使用"
      - "定期听力检查"
```

### 添加新的噪音类型

```yaml
noise_types:
  ocean_waves:  # 海浪声
    spectral_characteristics:
      low_freq_dominant: true
      rhythmic_pattern: true
    effectiveness: 0.75
    description: "自然海浪声，具有节律性"
    source_type: "natural"
    
  forest_ambience:  # 森林环境音
    spectral_characteristics:
      broad_spectrum: true
      natural_variation: true
    effectiveness: 0.70
    description: "森林环境音，包含鸟鸣和风声"
    source_type: "natural"
    
  mechanical_fan:  # 机械风扇
    spectral_characteristics:
      steady_state: true
      low_freq_emphasis: true
    effectiveness: 0.60
    description: "稳定的机械风扇声"
    source_type: "mechanical"
```

## 📊 配置验证规则

### 自定义验证规则

```yaml
validation:
  # 音频文件验证
  audio_file:
    min_duration_seconds: 10  # 最短10秒
    max_duration_seconds: 7200  # 最长2小时
    min_sample_rate: 16000
    max_sample_rate: 96000
    
  # 安全参数验证
  safety_parameters:
    volume_range: [0, 85]  # WHO推荐的安全音量范围
    distance_range: [10, 500]  # 合理的距离范围
    duration_range: [0.1, 12]  # 合理的使用时长
    
  # 效果值验证
  effectiveness_range: [0.0, 1.0]
  
  # 自定义验证函数
  custom_validators:
    - name: "medical_compliance"
      enabled: false
      strict_mode: true
    - name: "research_protocol"
      enabled: false
      data_quality_check: true
```

## 🚀 部署配置示例

### Docker容器配置

```yaml
# Docker部署配置
deployment:
  container:
    image: "sleep-audio-analyzer:2.0"
    resources:
      memory_limit: "1GB"
      cpu_limit: "2"
      
  volumes:
    - "/host/audio:/app/audio:ro"
    - "/host/config:/app/config:ro"
    - "/host/logs:/app/logs:rw"
    
  environment:
    CONFIG_MODE: "production"
    LOG_LEVEL: "INFO"
    ENABLE_HOT_RELOAD: "false"
```

### 云服务配置

```yaml
# 云服务部署配置
cloud:
  provider: "aws"
  region: "us-east-1"
  
  storage:
    audio_bucket: "sleep-audio-files"
    config_bucket: "sleep-audio-config"
    logs_bucket: "sleep-audio-logs"
    
  compute:
    instance_type: "t3.medium"
    auto_scaling: true
    min_instances: 1
    max_instances: 5
    
  security:
    encryption_at_rest: true
    encryption_in_transit: true
    access_logging: true
```

## 📝 配置管理最佳实践

1. **版本控制**
   - 所有配置文件纳入版本控制
   - 使用分支管理不同环境的配置
   - 记录配置变更历史

2. **环境分离**
   - 开发、测试、生产环境使用不同配置
   - 敏感信息使用环境变量
   - 配置文件模板化

3. **安全考虑**
   - 限制配置文件访问权限
   - 敏感配置加密存储
   - 定期审计配置变更

4. **监控和告警**
   - 监控配置文件变更
   - 配置验证失败告警
   - 性能指标监控

5. **文档维护**
   - 配置项详细说明
   - 变更记录和影响分析
   - 故障排除指南
