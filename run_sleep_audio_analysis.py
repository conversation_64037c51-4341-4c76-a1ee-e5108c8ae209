#!/usr/bin/env python3
"""
智能睡眠音频评估与推荐系统 - 主程序
基于科学研究的个性化睡眠音频分析
"""

import os
import sys
import argparse
import json
import numpy as np
from datetime import datetime
from pathlib import Path
from smart_sleep_audio_system import SmartSleepAudioSystem, UserGroup

# 导入插件系统
try:
    from plugin_system import get_plugin_manager, ReportGeneratorPlugin
    PLUGIN_SYSTEM_AVAILABLE = True
except ImportError:
    PLUGIN_SYSTEM_AVAILABLE = False
    print("⚠️  插件系统不可用，将使用内置报告生成器")

def get_china_timestamp():
    """获取中国时间戳，格式为 YYYYMMDD_HHMMSS"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def list_available_plugins():
    """列出所有可用的插件"""
    if not PLUGIN_SYSTEM_AVAILABLE:
        print("❌ 插件系统不可用")
        return

    try:
        plugin_manager = get_plugin_manager()
        plugins_info = plugin_manager.list_available_plugins()

        print("🔌 可用插件列表:")
        print("=" * 50)

        for plugin_type, plugins in plugins_info.items():
            if plugins:
                print(f"\n📂 {plugin_type.replace('_', ' ').title()}:")
                for plugin in plugins:
                    name = plugin.get('name', 'Unknown')
                    version = plugin.get('version', 'Unknown')
                    description = plugin.get('description', 'No description')
                    available = plugin.get('available', True)
                    dependencies = plugin.get('dependencies', [])

                    status = "✅" if available else "❌"
                    print(f"  {status} {name} (v{version})")
                    print(f"     {description}")

                    if dependencies:
                        deps_status = "需要依赖" if not available else "依赖已满足"
                        print(f"     📦 {deps_status}: {', '.join(dependencies)}")
                    print()
            else:
                print(f"\n📂 {plugin_type.replace('_', ' ').title()}: 无可用插件")

        print("💡 使用方法: --plugin <插件名称>")

    except Exception as e:
        print(f"❌ 获取插件列表失败: {e}")

def load_plugin_config(config_path):
    """加载插件配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️  加载插件配置失败: {e}")
        return {}

def generate_report_with_plugin(results, plugin_name, config, output_path=None):
    """使用插件生成报告"""
    if not PLUGIN_SYSTEM_AVAILABLE:
        print("❌ 插件系统不可用，使用内置生成器")
        return None

    try:
        plugin_manager = get_plugin_manager()

        # 获取插件实例
        plugin_instance = plugin_manager.get_plugin_instance('report_generators', plugin_name)
        if plugin_instance is None:
            print(f"❌ 未找到插件: {plugin_name}")
            available_plugins = plugin_manager.list_available_plugins().get('report_generators', [])
            if available_plugins:
                print("可用的报告生成插件:")
                for plugin in available_plugins:
                    print(f"  - {plugin.get('name', 'Unknown')}")
            return None

        # 准备配置
        plugin_config = config.copy()
        if output_path:
            plugin_config['output_path'] = output_path

        # 生成报告
        print(f"🔌 使用插件生成报告: {plugin_name}")
        result = plugin_instance.generate_report(results, plugin_config)

        return result

    except Exception as e:
        print(f"❌ 插件报告生成失败: {e}")
        return None

def convert_numpy_types(obj):
    """
    转换numpy类型为Python原生类型，解决JSON序列化问题

    Args:
        obj: 需要转换的对象，可以是任意类型

    Returns:
        转换后的对象，所有numpy类型都被转换为Python原生类型

    支持的转换类型:
        - np.floating (float32, float64) -> float
        - np.integer (int32, int64) -> int
        - np.ndarray -> list
        - dict -> 递归转换所有值
        - list -> 递归转换所有元素
        - 其他类型 -> 保持不变
    """
    if isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    else:
        return obj

def main():
    parser = argparse.ArgumentParser(
        description="智能睡眠音频评估与推荐系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_sleep_audio_analysis.py noisekun/waves.ogm
  python run_sleep_audio_analysis.py noisekun --all
  python run_sleep_audio_analysis.py noisekun/waterfall.ogm --format json
  python run_sleep_audio_analysis.py noisekun --user-group adult
        """
    )
    
    parser.add_argument(
        'input_path',
        nargs='?',  # 使input_path可选
        help='音频文件路径或包含音频文件的目录'
    )
    
    parser.add_argument(
        '--all',
        action='store_true',
        help='分析目录中的所有音频文件'
    )
    
    parser.add_argument(
        '--format',
        choices=['text', 'json', 'markdown'],
        default='text',
        help='输出格式 (默认: text)'
    )
    
    parser.add_argument(
        '--user-group',
        choices=['adult', 'infant', 'elderly', 'insomnia'],
        help='重点关注特定用户群体的推荐'
    )
    
    parser.add_argument(
        '--output',
        help='输出文件路径 (默认: 控制台输出)'
    )
    
    parser.add_argument(
        '--detailed',
        action='store_true',
        help='生成详细的分析报告'
    )

    parser.add_argument(
        '--auto-name',
        action='store_true',
        help='自动生成带时间戳的文件名'
    )

    parser.add_argument(
        '--comparison',
        action='store_true',
        help='生成技术参数对比表格（适用于多文件分析）'
    )

    parser.add_argument(
        '--template',
        choices=['standard', 'research', 'clinical', 'consumer'],
        default='standard',
        help='报告模板类型 (默认: standard)'
    )

    # 插件系统参数
    parser.add_argument(
        '--plugin',
        help='使用指定的报告生成插件 (如: HTMLReportGenerator, PDFReportGenerator)'
    )

    parser.add_argument(
        '--list-plugins',
        action='store_true',
        help='列出所有可用的插件'
    )

    parser.add_argument(
        '--plugin-config',
        help='插件配置文件路径 (JSON格式)'
    )

    args = parser.parse_args()

    # 处理插件相关命令
    if args.list_plugins:
        list_available_plugins()
        return

    # 检查是否提供了input_path
    if not args.input_path:
        print("错误: 需要提供音频文件路径或目录")
        parser.print_help()
        sys.exit(1)

    # 初始化系统
    print("🧠 初始化智能睡眠音频评估系统...")
    system = SmartSleepAudioSystem()
    
    # 确定要分析的文件
    input_path = Path(args.input_path)
    audio_files = []
    
    if input_path.is_file():
        audio_files = [input_path]
    elif input_path.is_dir():
        if args.all:
            # 支持的音频格式
            audio_extensions = {'.wav', '.mp3', '.flac', '.ogg', '.ogm', '.m4a', '.aac'}
            audio_files = [
                f for f in input_path.rglob('*') 
                if f.is_file() and f.suffix.lower() in audio_extensions
            ]
        else:
            print(f"错误: {input_path} 是目录，请使用 --all 参数分析所有文件")
            sys.exit(1)
    else:
        print(f"错误: 找不到文件或目录 {input_path}")
        sys.exit(1)
    
    if not audio_files:
        print("错误: 未找到音频文件")
        sys.exit(1)
    
    print(f"📁 找到 {len(audio_files)} 个音频文件")
    
    # 分析文件
    results = []
    for i, audio_file in enumerate(audio_files, 1):
        print(f"🔍 分析文件 {i}/{len(audio_files)}: {audio_file.name}")
        
        try:
            report = system.analyze_audio_file(str(audio_file))
            results.append(report)
            
            # 显示简要结果
            suitability_score = report.sleep_suitability.overall_score
            safety_level = report.safety_assessment.overall_safety.value
            print(f"   📊 睡眠适用性: {suitability_score:.1f}/100")
            print(f"   🛡️ 安全等级: {safety_level}")
            print(f"   🎯 总体推荐: {report.overall_recommendation}")
            
        except Exception as e:
            print(f"   ❌ 分析失败: {str(e)}")
            continue
        
        print()
    
    if not results:
        print("❌ 没有成功分析的文件")
        sys.exit(1)
    
    # 检查是否使用插件生成报告
    if args.plugin:
        # 加载插件配置
        plugin_config = {}
        if args.plugin_config:
            plugin_config = load_plugin_config(args.plugin_config)

        # 设置基本配置
        plugin_config.update({
            'title': '智能睡眠音频评估系统 - 分析报告',
            'template_style': 'modern',
            'include_charts': True,
            'user_group': args.user_group,
            'detailed': args.detailed,
            'comparison': args.comparison,
            'template': args.template
        })

        # 确定输出文件路径
        output_path = args.output
        if args.auto_name and not output_path:
            timestamp = get_china_timestamp()
            # 根据插件类型确定扩展名
            if 'HTML' in args.plugin:
                output_path = f"analysis_report_{timestamp}.html"
            elif 'PDF' in args.plugin:
                output_path = f"analysis_report_{timestamp}.pdf"
            elif 'CSV' in args.plugin:
                output_path = f"analysis_report_{timestamp}.csv"
            elif 'Excel' in args.plugin:
                output_path = f"analysis_report_{timestamp}.xlsx"
            else:
                output_path = f"analysis_report_{timestamp}.txt"

        # 使用插件生成报告
        result = generate_report_with_plugin(results, args.plugin, plugin_config, output_path)
        if result:
            print(f"✅ {result}")
        else:
            print("❌ 插件报告生成失败，使用内置生成器")
            # 降级到内置生成器
            args.plugin = None

    # 使用内置生成器（如果没有使用插件或插件失败）
    if not args.plugin:
        # 确定输出文件路径（支持自动命名）
        output_path = args.output
        if args.auto_name and not output_path:
            timestamp = get_china_timestamp()
            if args.format == 'markdown':
                output_path = f"analysis_report_{timestamp}.md"
            elif args.format == 'json':
                output_path = f"analysis_report_{timestamp}.json"
            else:
                output_path = f"analysis_report_{timestamp}.txt"

        # 生成输出
        if args.format == 'json':
            output_data = generate_json_output(results, args.user_group)
        elif args.format == 'markdown':
            output_data = generate_markdown_output(
                results,
                args.user_group,
                args.detailed,
                args.comparison,
                args.template,
                input_path
            )
        else:
            output_data = generate_text_output(results, args.user_group, args.detailed)

        # 输出结果
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output_data)
            print(f"📄 报告已保存到: {output_path}")
        else:
            print(output_data)

def generate_markdown_output(results, focus_user_group=None, detailed=False, comparison=False, template='standard', input_path=None):
    """生成Markdown格式输出"""
    lines = []
    timestamp = get_china_timestamp()
    current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

    # 根据模板类型调整标题
    if template == 'research':
        title = "# 🔬 智能睡眠音频评估系统 - 科研分析报告"
    elif template == 'clinical':
        title = "# 🏥 智能睡眠音频评估系统 - 临床评估报告"
    elif template == 'consumer':
        title = "# 🏠 智能睡眠音频评估系统 - 消费者指南"
    else:
        title = "# 🧠 智能睡眠音频评估系统 - 分析报告"

    lines.append(title)
    lines.append("")
    lines.append("## 📋 报告信息")
    lines.append("")
    lines.append(f"**生成时间**: {current_time} (中国时间)")
    lines.append(f"**时间戳**: {timestamp}")
    lines.append(f"**分析文件数**: {len(results)}")
    lines.append(f"**分析引擎**: 智能睡眠音频评估与推荐系统 v2.0")
    if input_path:
        lines.append(f"**分析目录**: {input_path}")
    if focus_user_group:
        lines.append(f"**重点用户群体**: {focus_user_group}")
    lines.append("")
    lines.append("---")
    lines.append("")

    # 排序结果
    sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)

    # 汇总表格
    lines.append("## 🏆 分析结果汇总")
    lines.append("")
    lines.append("| 排名 | 文件名 | 睡眠得分 | 噪音类型 | 安全等级 | 效果预测 | 推荐状态 |")
    lines.append("|------|--------|----------|----------|----------|----------|----------|")

    for i, result in enumerate(sorted_results, 1):
        filename = Path(result.audio_file).name
        score = result.sleep_suitability.overall_score
        noise_type = result.audio_features.noise_type.value
        safety = result.safety_assessment.overall_safety.value
        effectiveness = f"{result.sleep_suitability.effectiveness_prediction:.1%}"

        # 根据得分确定推荐状态
        if score >= 70:
            status = "✅ 强烈推荐"
        elif score >= 50:
            status = "⚠️ 可以使用"
        elif score >= 30:
            status = "🤔 谨慎使用"
        else:
            status = "❌ 不推荐"

        lines.append(f"| {i} | **{filename}** | {score:.1f}/100 | {noise_type} | {safety} | {effectiveness} | {status} |")

    lines.append("")
    lines.append("---")
    lines.append("")

    # 对比分析
    if comparison and len(results) > 1:
        lines.append("## 📊 技术参数对比分析")
        lines.append("")
        lines.append("### ⚙️ 详细技术参数")
        lines.append("")
        lines.append("| 文件名 | 频谱斜率 | 响度稳定性 | 动态范围(dB) | 音调峰值比 | 效果预测 |")
        lines.append("|--------|----------|------------|--------------|------------|----------|")

        for result in sorted_results:
            filename = Path(result.audio_file).name
            slope = result.audio_features.spectral_slope
            stability = result.audio_features.loudness_stability
            dynamic_range = result.audio_features.dynamic_range_db
            tonal_ratio = result.audio_features.tonal_ratio
            effectiveness = f"{result.sleep_suitability.effectiveness_prediction:.1%}"

            lines.append(f"| {filename} | {slope:.3f} | {stability:.3f} | {dynamic_range:.1f} | {tonal_ratio:.2f} | {effectiveness} |")

        lines.append("")

        # 科学有效性对比
        lines.append("### 🔬 科学有效性对比")
        lines.append("")
        lines.append("基于科学研究数据的效果对比：")
        lines.append("")

        for result in sorted_results:
            filename = Path(result.audio_file).name
            noise_type = result.audio_features.noise_type.value
            effectiveness = result.sleep_suitability.effectiveness_prediction

            lines.append(f"- **{filename}** ({noise_type}): {effectiveness:.1%} 预期有效性")

        lines.append("")
        lines.append("---")
        lines.append("")

    # 详细分析（如果启用）
    if detailed:
        lines.append("## 📁 详细分析结果")
        lines.append("")

        for i, result in enumerate(sorted_results, 1):
            filename = Path(result.audio_file).name
            lines.append(f"### {i}. 📁 {filename}")
            lines.append("")
            lines.append(f"**📊 睡眠适用性得分**: {result.sleep_suitability.overall_score:.1f}/100")
            lines.append("")

            # 音频特征
            lines.append("#### 🎵 音频特征分析")
            lines.append("")
            features = result.audio_features
            lines.append(f"- **噪音类型**: {features.noise_type.value}")
            lines.append(f"- **音频来源**: {features.audio_source.value}")
            lines.append(f"- **频谱斜率**: {features.spectral_slope:.3f}")
            lines.append(f"- **响度稳定性**: {features.loudness_stability:.3f}")
            lines.append(f"- **音调峰值比**: {features.tonal_ratio:.2f}")
            lines.append(f"- **动态范围**: {features.dynamic_range_db:.1f} dB")
            lines.append(f"- **声音标签**: {', '.join(features.sound_tags)}")
            lines.append("")

            # 安全评估
            lines.append("#### 🛡️ 安全性评估")
            lines.append("")
            safety = result.safety_assessment
            lines.append(f"- **总体安全等级**: {safety.overall_safety.value}")
            lines.append(f"- **推荐音量**: {safety.recommended_volume_db[0]}-{safety.recommended_volume_db[1]} dB")
            lines.append(f"- **推荐距离**: {safety.recommended_distance_cm} cm")
            if safety.warnings:
                lines.append(f"- **安全警告**: {'; '.join(safety.warnings)}")
            lines.append("")

            # 用户群体推荐
            lines.append("#### 👥 用户群体推荐")
            lines.append("")

            # 显示所有用户群体的推荐（如果有的话）
            user_groups = [
                ('adult', UserGroup.ADULT, '成人'),
                ('infant', UserGroup.INFANT, '婴幼儿'),
                ('elderly', UserGroup.ELDERLY, '老年人'),
                ('insomnia', UserGroup.INSOMNIA, '失眠患者')
            ]

            for group_key, group_enum, group_name in user_groups:
                if group_enum in result.personalized_recommendations:
                    rec = result.personalized_recommendations[group_enum]
                    lines.append(f"**{group_name}**:")
                    lines.append(f"- 推荐得分: {rec.suitability_score:.1f}/100")
                    lines.append(f"- 使用建议: {rec.usage_recommendation}")
                    lines.append(f"- 主要益处: {', '.join(rec.benefits)}")
                    lines.append(f"- 潜在风险: {', '.join(rec.risks)}")
                    lines.append(f"- 科学依据: {rec.scientific_rationale}")
                    lines.append("")

            lines.append(f"**🎯 总体推荐**: {result.overall_recommendation}")
            lines.append("")
            lines.append("---")
            lines.append("")

    # 性能统计（如果是多文件分析）
    if len(results) > 1:
        lines.append("## 📈 性能统计")
        lines.append("")
        scores = [result.sleep_suitability.overall_score for result in results]
        avg_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)
        score_diff = max_score - min_score

        lines.append(f"- **平均睡眠适用性得分**: {avg_score:.1f}/100")
        lines.append(f"- **最高得分**: {max_score:.1f}/100")
        lines.append(f"- **最低得分**: {min_score:.1f}/100")
        lines.append(f"- **得分差异**: {score_diff:.1f}分")
        lines.append("")
        lines.append("---")
        lines.append("")

    # 科学依据和建议
    lines.append("## 🔬 科学依据与使用建议")
    lines.append("")
    lines.append("### 📚 科学研究依据")
    lines.append("")
    lines.append("本分析基于以下科学研究数据：")
    lines.append("")
    lines.append("1. **粉噪音效果**: 82%的研究显示粉噪音对睡眠有积极影响")
    lines.append("2. **白噪音效果**: 33%的研究显示白噪音对睡眠有积极影响")
    lines.append("3. **棕噪音效果**: 基于低频偏好理论，估计65%有效性")
    lines.append("4. **心理声学原理**: 采用A-weighting和Bark尺度分析")
    lines.append("5. **安全标准**: 基于WHO和相关医学研究的音量安全阈值")
    lines.append("")

    # 使用建议
    lines.append("### 🎯 使用建议")
    lines.append("")
    lines.append("#### 💡 一般使用原则")
    lines.append("")
    lines.append("1. **音量控制**: 成人≤60dB，婴幼儿≤50dB，距离≥2米")
    lines.append("2. **使用时间**: 建议睡前30分钟开始播放，入睡后可继续")
    lines.append("3. **环境配置**: 在安静的卧室环境中使用效果最佳")
    lines.append("4. **个体差异**: 根据个人偏好和反应调整使用方式")
    lines.append("")

    lines.append("#### 🚨 安全注意事项")
    lines.append("")
    lines.append("1. **婴幼儿使用**: 严格控制音量和距离，避免长时间连续使用")
    lines.append("2. **听力保护**: 定期检查听力，如有不适立即停止使用")
    lines.append("3. **依赖性**: 避免过度依赖，建议间歇性使用")
    lines.append("4. **医疗咨询**: 有听力问题或睡眠障碍者请咨询医生")
    lines.append("")

    # 根据模板添加特定建议
    if template == 'clinical':
        lines.append("### 🏥 临床使用指导")
        lines.append("")
        lines.append("1. **患者评估**: 使用前评估患者听力状况和睡眠障碍类型")
        lines.append("2. **剂量控制**: 严格按照推荐音量和时长使用")
        lines.append("3. **效果监测**: 定期评估治疗效果和副作用")
        lines.append("4. **个体化调整**: 根据患者反应调整使用方案")
    elif template == 'research':
        lines.append("### 🔬 研究方法说明")
        lines.append("")
        lines.append("1. **分析算法**: 基于心理声学模型和机器学习算法")
        lines.append("2. **数据来源**: 多项国际睡眠研究的荟萃分析")
        lines.append("3. **统计方法**: 使用线性回归和频谱分析")
        lines.append("4. **验证标准**: 符合IEC 61672-1国际标准")

    lines.append("")
    lines.append("---")
    lines.append("")
    lines.append(f"**报告生成**: 智能睡眠音频评估与推荐系统 v2.0")
    lines.append(f"**技术支持**: 基于librosa、scipy和numpy的专业音频分析")
    lines.append(f"**数据来源**: 《白噪音对睡眠影响的科学分析报告》及相关研究文献")

    return "\n".join(lines)

def generate_text_output(results, focus_user_group=None, detailed=False):
    """生成文本格式输出"""
    lines = []
    
    # 标题
    lines.append("🧠 智能睡眠音频评估与推荐系统 - 分析报告")
    lines.append("=" * 80)
    lines.append(f"📊 分析文件数量: {len(results)}")
    lines.append("")
    
    # 汇总统计
    lines.append("📈 汇总统计")
    lines.append("-" * 40)
    
    # 按噪音类型统计
    noise_type_stats = {}
    safety_stats = {}
    suitability_scores = []
    
    for result in results:
        noise_type = result.audio_features.noise_type.value
        safety_level = result.safety_assessment.overall_safety.value
        
        noise_type_stats[noise_type] = noise_type_stats.get(noise_type, 0) + 1
        safety_stats[safety_level] = safety_stats.get(safety_level, 0) + 1
        suitability_scores.append(result.sleep_suitability.overall_score)
    
    lines.append("🎵 噪音类型分布:")
    for noise_type, count in noise_type_stats.items():
        lines.append(f"   • {noise_type}: {count} 个文件")
    
    lines.append("🛡️ 安全等级分布:")
    for safety_level, count in safety_stats.items():
        lines.append(f"   • {safety_level}: {count} 个文件")
    
    if suitability_scores:
        avg_score = sum(suitability_scores) / len(suitability_scores)
        max_score = max(suitability_scores)
        min_score = min(suitability_scores)
        lines.append(f"📊 睡眠适用性得分: 平均 {avg_score:.1f}, 最高 {max_score:.1f}, 最低 {min_score:.1f}")
    
    lines.append("")
    
    # 推荐排序
    lines.append("🏆 推荐排序 (按睡眠适用性得分)")
    lines.append("-" * 40)
    
    sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)
    
    for i, result in enumerate(sorted_results, 1):
        filename = Path(result.audio_file).name
        score = result.sleep_suitability.overall_score
        noise_type = result.audio_features.noise_type.value
        safety = result.safety_assessment.overall_safety.value
        
        lines.append(f"{i:2d}. {filename}")
        lines.append(f"    📊 得分: {score:.1f}/100 | 🎵 类型: {noise_type} | 🛡️ 安全: {safety}")
        
        # 如果指定了用户群体，显示该群体的推荐
        if focus_user_group:
            user_group_enum = {
                'adult': UserGroup.ADULT,
                'infant': UserGroup.INFANT,
                'elderly': UserGroup.ELDERLY,
                'insomnia': UserGroup.INSOMNIA
            }[focus_user_group]
            
            if user_group_enum in result.personalized_recommendations:
                rec = result.personalized_recommendations[user_group_enum]
                lines.append(f"    👤 {user_group_enum.value}: {rec.suitability_score:.1f}/100 - {rec.usage_recommendation}")
        
        lines.append("")
    
    # 详细报告
    if detailed:
        lines.append("📋 详细分析报告")
        lines.append("=" * 80)
        
        for result in sorted_results:
            detailed_report = SmartSleepAudioSystem().generate_detailed_report(result)
            lines.append(detailed_report)
            lines.append("")
    
    return "\n".join(lines)

def generate_json_output(results, focus_user_group=None):
    """
    生成JSON格式输出，自动处理numpy类型序列化问题

    Args:
        results: 分析结果列表
        focus_user_group: 重点关注的用户群体

    Returns:
        JSON格式的字符串，所有numpy类型已转换为Python原生类型
    """
    try:
        output_data = {
            'analysis_summary': {
                'total_files': len(results),
                'analysis_timestamp': results[0].analysis_timestamp if results else None,
                'system_version': '2.0'
            },
            'files': []
        }

        for result in results:
            file_data = {
                'filename': Path(result.audio_file).name,
                'audio_features': {
                    'noise_type': result.audio_features.noise_type.value,
                    'audio_source': result.audio_features.audio_source.value,
                    'sound_tags': result.audio_features.sound_tags,
                    'spectral_slope': result.audio_features.spectral_slope,
                    'loudness_stability': result.audio_features.loudness_stability,
                    'tonal_ratio': result.audio_features.tonal_ratio,
                    'dynamic_range_db': result.audio_features.dynamic_range_db,
                    'duration_seconds': result.audio_features.duration_seconds
                },
                'safety_assessment': {
                    'overall_safety': result.safety_assessment.overall_safety.value,
                    'safety_score': result.safety_assessment.safety_score,
                    'warnings': result.safety_assessment.warnings,
                    'recommended_volume_db': result.safety_assessment.recommended_volume_db,
                    'recommended_distance_cm': result.safety_assessment.recommended_distance_cm,
                    'max_duration_hours': result.safety_assessment.max_duration_hours
                },
                'sleep_suitability': {
                    'overall_score': result.sleep_suitability.overall_score,
                    'effectiveness_prediction': result.sleep_suitability.effectiveness_prediction,
                    'disruption_risk': result.sleep_suitability.disruption_risk,
                    'comfort_level': result.sleep_suitability.comfort_level,
                    'scientific_evidence_level': result.sleep_suitability.scientific_evidence_level
                },
                'overall_recommendation': result.overall_recommendation
            }

            # 添加个性化推荐
            if focus_user_group:
                user_group_enum = {
                    'adult': UserGroup.ADULT,
                    'infant': UserGroup.INFANT,
                    'elderly': UserGroup.ELDERLY,
                    'insomnia': UserGroup.INSOMNIA
                }[focus_user_group]

                if user_group_enum in result.personalized_recommendations:
                    rec = result.personalized_recommendations[user_group_enum]
                    file_data['personalized_recommendation'] = {
                        'user_group': rec.user_group.value,
                        'suitability_score': rec.suitability_score,
                        'usage_recommendation': rec.usage_recommendation,
                        'optimal_settings': rec.optimal_settings,
                        'benefits': rec.benefits,
                        'risks': rec.risks,
                        'alternatives': rec.alternatives,
                        'scientific_rationale': rec.scientific_rationale
                    }

            output_data['files'].append(file_data)

        # 按得分排序
        output_data['files'].sort(key=lambda x: x['sleep_suitability']['overall_score'], reverse=True)

        # 转换numpy类型
        converted_data = convert_numpy_types(output_data)

        # 生成JSON字符串
        return json.dumps(converted_data, ensure_ascii=False, indent=2)

    except Exception as e:
        # 降级处理：如果转换失败，返回基本信息
        print(f"⚠️  JSON生成过程中出现错误: {e}")
        print("🔄 使用降级模式生成基本报告...")

        fallback_data = {
            'error': 'JSON序列化部分失败',
            'message': str(e),
            'analysis_summary': {
                'total_files': len(results) if results else 0,
                'system_version': '2.0',
                'fallback_mode': True
            },
            'basic_results': []
        }

        # 尝试提取基本信息
        try:
            for result in results:
                basic_info = {
                    'filename': Path(result.audio_file).name,
                    'noise_type': result.audio_features.noise_type.value,
                    'sleep_score': float(result.sleep_suitability.overall_score) if hasattr(result.sleep_suitability.overall_score, 'item') else result.sleep_suitability.overall_score,
                    'safety_level': result.safety_assessment.overall_safety.value,
                    'recommendation': result.overall_recommendation
                }
                fallback_data['basic_results'].append(basic_info)
        except Exception as fallback_error:
            fallback_data['fallback_error'] = str(fallback_error)

        return json.dumps(fallback_data, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    main()
