<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能睡眠音频评估系统 - 专业分析报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #2196F3;
            margin-bottom: 30px;
        }
        
        .report-header h1 {
            color: #2196F3;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .report-meta {
            color: #666;
            font-size: 0.9em;
        }
        
        .report-meta span {
            margin: 0 15px;
        }
        
        .view-toggle {
            text-align: center;
            margin: 20px 0;
        }
        
        .toggle-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .toggle-btn:hover {
            background: #1976D2;
        }
        
        .toggle-btn.active {
            background: #1976D2;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        
        .section h2 {
            color: #2196F3;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary-card h3 {
            color: #2196F3;
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .ranking-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ranking-table th,
        .ranking-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .ranking-table th {
            background: #2196F3;
            color: white;
            font-weight: 600;
        }
        
        .ranking-table tr:hover {
            background: #f5f5f5;
        }
        
        .score-excellent { color: #4CAF50; font-weight: bold; }
        .score-good { color: #2196F3; font-weight: bold; }
        .score-fair { color: #FF9800; font-weight: bold; }
        .score-poor { color: #F44336; font-weight: bold; }
        
        .status-excellent { background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-good { background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-fair { background: #FF9800; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-poor { background: #F44336; color: white; padding: 4px 8px; border-radius: 4px; }
        
        .file-detail {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #2196F3;
        }
        
        .file-detail h3 {
            color: #2196F3;
            font-size: 1.4em;
            margin-bottom: 15px;
            border-bottom: 2px solid #e3f2fd;
            padding-bottom: 10px;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .detail-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 3px solid #2196F3;
        }
        
        .collapsible {
            background: #e3f2fd;
            color: #1976D2;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 16px;
            font-weight: 600;
            border-radius: 5px;
            margin: 10px 0;
            transition: background 0.3s;
        }
        
        .collapsible:hover {
            background: #bbdefb;
        }
        
        .collapsible.active {
            background: #2196F3;
            color: white;
        }
        
        .collapsible:after {
            content: '\002B';
            color: #1976D2;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        
        .collapsible.active:after {
            content: "\2212";
            color: white;
        }
        
        .content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: #f8f9fa;
            border-radius: 0 0 5px 5px;
        }
        
        .content.active {
            padding: 18px;
            max-height: 1000px;
        }
        
        .tech-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        
        .tech-table th,
        .tech-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        
        .tech-table th {
            background: #2196F3;
            color: white;
            font-weight: 600;
        }
        
        .tech-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .user-group {
            background: #fff;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .user-group h4 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .user-group .score {
            font-weight: bold;
            color: #2196F3;
            font-size: 1.1em;
        }
        
        .scientific-evidence {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 15px 0;
        }
        
        .scientific-evidence h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .safety-warning {
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        
        .safety-warning h4 {
            color: #f57c00;
            margin-bottom: 10px;
        }
        
        .professional-view {
            display: block;
        }
        
        .simple-view {
            display: none;
        }
        
        .report-footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 30px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .report-header h1 {
                font-size: 2em;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>智能睡眠音频评估系统 - 专业分析报告</h1>
            <div class="report-meta">
                <span class="timestamp">生成时间: 2025年06月26日 12:35:00</span>
                <span class="version">系统版本: 2.0 (专业增强版)</span>
            </div>
        </header>
        
        <div class="view-toggle">
            <button class="toggle-btn active" onclick="toggleView('professional')">🔬 专业视图</button>
            <button class="toggle-btn" onclick="toggleView('simple')">📋 简化视图</button>
        </div>
        
        <main class="report-content">
            <div class="section">
                <h2>📊 分析摘要</h2>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>3</h3>
                        <p>分析文件总数</p>
                    </div>
                    <div class="summary-card">
                        <h3>1</h3>
                        <p>优秀文件 (≥80分)</p>
                    </div>
                    <div class="summary-card">
                        <h3>2</h3>
                        <p>良好文件 (60-79分)</p>
                    </div>
                    <div class="summary-card">
                        <h3>72.5</h3>
                        <p>平均得分</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🏆 文件排名</h2>
                <table class="ranking-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>文件名</th>
                            <th>睡眠适用性得分</th>
                            <th>噪音类型</th>
                            <th>安全等级</th>
                            <th>推荐状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><strong>white-noise.wav</strong></td>
                            <td class="score-excellent">89.7/100</td>
                            <td>白噪音</td>
                            <td>安全</td>
                            <td><span class="status-excellent">✅ 强烈推荐</span></td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td><strong>pink-noise.wav</strong></td>
                            <td class="score-good">66.9/100</td>
                            <td>粉噪音</td>
                            <td>需要注意</td>
                            <td><span class="status-good">⚠️ 可以使用</span></td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td><strong>brown-noise.wav</strong></td>
                            <td class="score-good">61.0/100</td>
                            <td>棕噪音</td>
                            <td>需要注意</td>
                            <td><span class="status-good">⚠️ 可以使用</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="section professional-view">
                <h2>📊 技术参数汇总对比表格</h2>
                <table class="tech-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>white-noise.wav</th>
                            <th>pink-noise.wav</th>
                            <th>brown-noise.wav</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>睡眠适用性得分</strong></td>
                            <td>89.7/100</td>
                            <td>66.9/100</td>
                            <td>61.0/100</td>
                        </tr>
                        <tr>
                            <td><strong>噪音类型</strong></td>
                            <td>白噪音</td>
                            <td>粉噪音</td>
                            <td>棕噪音</td>
                        </tr>
                        <tr>
                            <td><strong>频谱斜率</strong></td>
                            <td>-0.001</td>
                            <td>-1.084</td>
                            <td>-2.041</td>
                        </tr>
                        <tr>
                            <td><strong>响度稳定性</strong></td>
                            <td>0.029</td>
                            <td>0.104</td>
                            <td>0.266</td>
                        </tr>
                        <tr>
                            <td><strong>动态范围(dB)</strong></td>
                            <td>3.7</td>
                            <td>7.3</td>
                            <td>19.8</td>
                        </tr>
                        <tr>
                            <td><strong>音调峰值比</strong></td>
                            <td>1.32</td>
                            <td>1207.29</td>
                            <td>1745963.38</td>
                        </tr>
                        <tr>
                            <td><strong>效果预测</strong></td>
                            <td>29.6%</td>
                            <td>54.8%</td>
                            <td>39.6%</td>
                        </tr>
                        <tr>
                            <td><strong>舒适度</strong></td>
                            <td>89.7%</td>
                            <td>66.9%</td>
                            <td>67.1%</td>
                        </tr>
                        <tr>
                            <td><strong>总体安全等级</strong></td>
                            <td>安全</td>
                            <td>需要注意</td>
                            <td>需要注意</td>
                        </tr>
                        <tr>
                            <td><strong>科学证据等级</strong></td>
                            <td>有限证据支持 (33%研究有效)</td>
                            <td>强证据支持 (82%研究有效)</td>
                            <td>理论支持 (低频偏好)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>📋 详细分析</h2>

                <!-- White Noise Analysis -->
                <div class="file-detail">
                    <h3>🎵 white-noise.wav</h3>
                    <div class="detail-grid">
                        <div class="detail-item"><strong>睡眠适用性得分:</strong> 89.7/100</div>
                        <div class="detail-item"><strong>噪音类型:</strong> 白噪音</div>
                        <div class="detail-item"><strong>安全等级:</strong> 安全</div>
                        <div class="detail-item"><strong>效果预测:</strong> 29.6%</div>
                    </div>
                    <p><strong>总体推荐:</strong> ✅ 强烈推荐：科学证据支持，安全性良好</p>

                    <button type="button" class="collapsible">🎵 音频特征分析</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>噪音类型</strong></td><td>白噪音</td></tr>
                            <tr><td><strong>音频来源</strong></td><td>混合声音</td></tr>
                            <tr><td><strong>频谱斜率</strong></td><td>-0.001</td></tr>
                            <tr><td><strong>响度稳定性</strong></td><td>0.029</td></tr>
                            <tr><td><strong>音调峰值比</strong></td><td>1.32</td></tr>
                            <tr><td><strong>动态范围</strong></td><td>3.7 dB</td></tr>
                            <tr><td><strong>声音标签</strong></td><td>High Frequency Content</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible">🛡️ 安全性评估</button>
                    <div class="content">
                        <div class="safety-warning">
                            <h4>🛡️ 安全参数</h4>
                            <ul>
                                <li><strong>总体安全等级:</strong> 安全</li>
                                <li><strong>音量安全:</strong> 安全</li>
                                <li><strong>内容安全:</strong> 安全</li>
                                <li><strong>推荐音量:</strong> 45-60 dB</li>
                                <li><strong>推荐距离:</strong> 50 cm</li>
                                <li><strong>最大使用时长:</strong> 8.0 小时</li>
                            </ul>
                        </div>
                    </div>

                    <button type="button" class="collapsible">😴 睡眠适用性评估</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>效果预测</strong></td><td>29.6%</td></tr>
                            <tr><td><strong>干扰风险</strong></td><td>0.0%</td></tr>
                            <tr><td><strong>舒适度</strong></td><td>89.7%</td></tr>
                            <tr><td><strong>科学证据等级</strong></td><td>有限证据支持 (33%研究有效)</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible professional-view">👥 用户群体推荐</button>
                    <div class="content professional-view">
                        <div class="user-group">
                            <h4>👨‍💼 成人</h4>
                            <p class="score">推荐得分: 89.7/100</p>
                            <p><strong>使用建议:</strong> 可以使用：注意控制音量在50-60dB，避免整夜高音量播放</p>
                            <p><strong>主要益处:</strong> 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声</p>
                            <p><strong>潜在风险:</strong> 长期使用可能产生依赖</p>
                            <p><strong>科学依据:</strong> 白噪音仅在33%的研究中显示有效，效果有限</p>
                        </div>

                        <div class="user-group">
                            <h4>👶 婴幼儿</h4>
                            <p class="score">推荐得分: 62.8/100</p>
                            <p><strong>使用建议:</strong> 谨慎使用：音量≤50dB，距离≥2米，仅在入睡阶段使用，入睡后关闭</p>
                            <p><strong>主要益处:</strong> 辅助快速入睡, 创造一致的睡眠环境</p>
                            <p><strong>潜在风险:</strong> 需严格控制音量和距离, 避免长时间连续使用</p>
                            <p><strong>科学依据:</strong> 研究显示80%的新生儿在5分钟内被白噪音哄睡，但需严格控制安全参数</p>
                        </div>

                        <div class="user-group">
                            <h4>👴 老年人</h4>
                            <p class="score">推荐得分: 89.7/100</p>
                            <p><strong>使用建议:</strong> 可以尝试：虽非最佳选择，但可作为环境噪声屏蔽</p>
                            <p><strong>主要益处:</strong> 增强深度睡眠, 改善记忆巩固, 减少环境干扰</p>
                            <p><strong>潜在风险:</strong> 避免过度依赖, 注意听力保护</p>
                            <p><strong>科学依据:</strong> 粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现</p>
                        </div>

                        <div class="user-group">
                            <h4>😴 失眠患者</h4>
                            <p class="score">推荐得分: 98.7/100</p>
                            <p><strong>使用建议:</strong> 可以尝试：作为环境噪声屏蔽，但需配合其他治疗方法</p>
                            <p><strong>主要益处:</strong> 辅助放松, 屏蔽干扰, 建立睡眠仪式感</p>
                            <p><strong>潜在风险:</strong> 仅为辅助手段, 需配合其他治疗方法</p>
                            <p><strong>科学依据:</strong> 白噪音可使失眠患者入睡时间缩短38%，但仅作为辅助治疗</p>
                        </div>
                    </div>

                    <div class="scientific-evidence professional-view">
                        <h4>🔬 科学依据</h4>
                        <p>白噪音仅在33%的研究中显示有效，效果有限。虽然传统上被广泛使用，但科学证据相对较弱。建议作为环境噪声屏蔽使用，而非主要的睡眠改善手段。</p>
                    </div>
                </div>

                <!-- Pink Noise Analysis -->
                <div class="file-detail">
                    <h3>🎵 pink-noise.wav</h3>
                    <div class="detail-grid">
                        <div class="detail-item"><strong>睡眠适用性得分:</strong> 66.9/100</div>
                        <div class="detail-item"><strong>噪音类型:</strong> 粉噪音</div>
                        <div class="detail-item"><strong>安全等级:</strong> 需要注意</div>
                        <div class="detail-item"><strong>效果预测:</strong> 54.8%</div>
                    </div>
                    <p><strong>总体推荐:</strong> ⚠️ 可以使用：有一定效果，注意使用方法</p>

                    <button type="button" class="collapsible">🎵 音频特征分析</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>噪音类型</strong></td><td>粉噪音</td></tr>
                            <tr><td><strong>音频来源</strong></td><td>混合声音</td></tr>
                            <tr><td><strong>频谱斜率</strong></td><td>-1.084</td></tr>
                            <tr><td><strong>响度稳定性</strong></td><td>0.104</td></tr>
                            <tr><td><strong>音调峰值比</strong></td><td>1207.29</td></tr>
                            <tr><td><strong>动态范围</strong></td><td>7.3 dB</td></tr>
                            <tr><td><strong>声音标签</strong></td><td>High Frequency Content</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible">🛡️ 安全性评估</button>
                    <div class="content">
                        <div class="safety-warning">
                            <h4>⚠️ 安全参数</h4>
                            <ul>
                                <li><strong>总体安全等级:</strong> 需要注意</li>
                                <li><strong>音量安全:</strong> 安全</li>
                                <li><strong>内容安全:</strong> 需要注意</li>
                                <li><strong>推荐音量:</strong> 45-60 dB</li>
                                <li><strong>推荐距离:</strong> 50 cm</li>
                                <li><strong>最大使用时长:</strong> 8.0 小时</li>
                                <li><strong>安全警告:</strong> 包含明显音调成分，可能影响睡眠</li>
                            </ul>
                        </div>
                    </div>

                    <button type="button" class="collapsible">😴 睡眠适用性评估</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>效果预测</strong></td><td>54.8%</td></tr>
                            <tr><td><strong>干扰风险</strong></td><td>30.0%</td></tr>
                            <tr><td><strong>舒适度</strong></td><td>66.9%</td></tr>
                            <tr><td><strong>科学证据等级</strong></td><td>强证据支持 (82%研究有效)</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible professional-view">👥 用户群体推荐</button>
                    <div class="content professional-view">
                        <div class="user-group">
                            <h4>👨‍💼 成人</h4>
                            <p class="score">推荐得分: 80.2/100</p>
                            <p><strong>使用建议:</strong> 可以使用：注意控制音量在50-60dB，避免整夜高音量播放</p>
                            <p><strong>主要益处:</strong> 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声</p>
                            <p><strong>潜在风险:</strong> 长期使用可能产生依赖</p>
                            <p><strong>科学依据:</strong> 粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%</p>
                        </div>

                        <div class="user-group">
                            <h4>👶 婴幼儿</h4>
                            <p class="score">推荐得分: 46.8/100</p>
                            <p><strong>使用建议:</strong> 不推荐使用：不符合婴幼儿安全标准</p>
                            <p><strong>主要益处:</strong> 辅助快速入睡, 创造一致的睡眠环境</p>
                            <p><strong>潜在风险:</strong> 需严格控制音量和距离, 避免长时间连续使用</p>
                            <p><strong>科学依据:</strong> 粉噪音在82%的科学研究中显示有效，但婴幼儿需要更严格的安全标准</p>
                        </div>

                        <div class="user-group">
                            <h4>👴 老年人</h4>
                            <p class="score">推荐得分: 86.9/100</p>
                            <p><strong>使用建议:</strong> 推荐使用：低频丰富的声音有助于深度睡眠，建议音量45-55dB</p>
                            <p><strong>主要益处:</strong> 增强深度睡眠, 改善记忆巩固, 减少环境干扰</p>
                            <p><strong>潜在风险:</strong> 避免过度依赖, 注意听力保护</p>
                            <p><strong>科学依据:</strong> 粉噪音可增强老年人深度睡眠的慢波活动，改善记忆表现</p>
                        </div>

                        <div class="user-group">
                            <h4>😴 失眠患者</h4>
                            <p class="score">推荐得分: 66.9/100</p>
                            <p><strong>使用建议:</strong> 不推荐使用：建议咨询专业医生选择更合适的治疗方案</p>
                            <p><strong>主要益处:</strong> 辅助放松, 屏蔽干扰, 建立睡眠仪式感</p>
                            <p><strong>潜在风险:</strong> 仅为辅助手段, 需配合其他治疗方法</p>
                            <p><strong>科学依据:</strong> 粉噪音在82%的科学研究中显示有效，但失眠患者需要专业治疗</p>
                        </div>
                    </div>

                    <div class="scientific-evidence professional-view">
                        <h4>🔬 科学依据</h4>
                        <p>粉噪音在82%的科学研究中显示有效，明显优于白噪音的33%。低频丰富的特性使其更适合深度睡眠，特别是对老年人群体效果显著。</p>
                    </div>
                </div>

                <!-- Brown Noise Analysis -->
                <div class="file-detail">
                    <h3>🎵 brown-noise.wav</h3>
                    <div class="detail-grid">
                        <div class="detail-item"><strong>睡眠适用性得分:</strong> 61.0/100</div>
                        <div class="detail-item"><strong>噪音类型:</strong> 棕噪音</div>
                        <div class="detail-item"><strong>安全等级:</strong> 需要注意</div>
                        <div class="detail-item"><strong>效果预测:</strong> 39.6%</div>
                    </div>
                    <p><strong>总体推荐:</strong> ⚠️ 可以使用：有一定效果，注意使用方法</p>

                    <button type="button" class="collapsible">🎵 音频特征分析</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>噪音类型</strong></td><td>棕噪音</td></tr>
                            <tr><td><strong>音频来源</strong></td><td>自然声音</td></tr>
                            <tr><td><strong>频谱斜率</strong></td><td>-2.041</td></tr>
                            <tr><td><strong>响度稳定性</strong></td><td>0.266</td></tr>
                            <tr><td><strong>音调峰值比</strong></td><td>1745963.38</td></tr>
                            <tr><td><strong>动态范围</strong></td><td>19.8 dB</td></tr>
                            <tr><td><strong>声音标签</strong></td><td>Low Frequency Dominant</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible">🛡️ 安全性评估</button>
                    <div class="content">
                        <div class="safety-warning">
                            <h4>⚠️ 安全参数</h4>
                            <ul>
                                <li><strong>总体安全等级:</strong> 需要注意</li>
                                <li><strong>音量安全:</strong> 安全</li>
                                <li><strong>内容安全:</strong> 需要注意</li>
                                <li><strong>推荐音量:</strong> 45-60 dB</li>
                                <li><strong>推荐距离:</strong> 50 cm</li>
                                <li><strong>最大使用时长:</strong> 8.0 小时</li>
                                <li><strong>安全警告:</strong> 包含明显音调成分，可能影响睡眠</li>
                            </ul>
                        </div>
                    </div>

                    <button type="button" class="collapsible">😴 睡眠适用性评估</button>
                    <div class="content">
                        <table class="tech-table">
                            <tr><td><strong>效果预测</strong></td><td>39.6%</td></tr>
                            <tr><td><strong>干扰风险</strong></td><td>30.0%</td></tr>
                            <tr><td><strong>舒适度</strong></td><td>67.1%</td></tr>
                            <tr><td><strong>科学证据等级</strong></td><td>理论支持 (低频偏好)</td></tr>
                        </table>
                    </div>

                    <button type="button" class="collapsible professional-view">👥 用户群体推荐</button>
                    <div class="content professional-view">
                        <div class="user-group">
                            <h4>👨‍💼 成人</h4>
                            <p class="score">推荐得分: 61.0/100</p>
                            <p><strong>使用建议:</strong> 可以使用：注意控制音量在50-60dB，避免整夜高音量播放</p>
                            <p><strong>主要益处:</strong> 改善入睡时间, 减少夜间觉醒, 屏蔽环境噪声</p>
                            <p><strong>潜在风险:</strong> 长期使用可能产生依赖</p>
                            <p><strong>科学依据:</strong> 自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好</p>
                        </div>

                        <div class="user-group">
                            <h4>👶 婴幼儿</h4>
                            <p class="score">推荐得分: 42.7/100</p>
                            <p><strong>使用建议:</strong> 不推荐使用：不符合婴幼儿安全标准</p>
                            <p><strong>主要益处:</strong> 辅助快速入睡, 创造一致的睡眠环境</p>
                            <p><strong>潜在风险:</strong> 需严格控制音量和距离, 避免长时间连续使用</p>
                            <p><strong>科学依据:</strong> 自然声音通常属于粉噪音或棕噪音，但婴幼儿需要更严格的安全标准</p>
                        </div>

                        <div class="user-group">
                            <h4>👴 老年人</h4>
                            <p class="score">推荐得分: 79.3/100</p>
                            <p><strong>使用建议:</strong> 推荐使用：低频丰富的声音有助于深度睡眠，建议音量45-55dB</p>
                            <p><strong>主要益处:</strong> 增强深度睡眠, 改善记忆巩固, 减少环境干扰</p>
                            <p><strong>潜在风险:</strong> 避免过度依赖, 注意听力保护</p>
                            <p><strong>科学依据:</strong> 自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好</p>
                        </div>

                        <div class="user-group">
                            <h4>😴 失眠患者</h4>
                            <p class="score">推荐得分: 61.0/100</p>
                            <p><strong>使用建议:</strong> 不推荐使用：建议咨询专业医生选择更合适的治疗方案</p>
                            <p><strong>主要益处:</strong> 辅助放松, 屏蔽干扰, 建立睡眠仪式感</p>
                            <p><strong>潜在风险:</strong> 仅为辅助手段, 需配合其他治疗方法</p>
                            <p><strong>科学依据:</strong> 自然声音通常属于粉噪音或棕噪音，但失眠患者需要专业治疗</p>
                        </div>
                    </div>

                    <div class="scientific-evidence professional-view">
                        <h4>🔬 科学依据</h4>
                        <p>棕噪音基于低频偏好理论，自然声音通常属于粉噪音或棕噪音，低频丰富，更受用户偏好。虽然直接研究较少，但理论基础较为扎实。</p>
                    </div>
                </div>
            </div>

            <div class="section professional-view">
                <h2>🔬 科学依据与推荐建议</h2>

                <div class="scientific-evidence">
                    <h4>📚 科学研究依据</h4>
                    <p>本分析基于以下科学研究数据：</p>
                    <ul>
                        <li><strong>粉噪音效果</strong>: 82%的研究显示粉噪音对睡眠有积极影响</li>
                        <li><strong>白噪音效果</strong>: 33%的研究显示白噪音对睡眠有积极影响</li>
                        <li><strong>棕噪音效果</strong>: 基于低频偏好理论，估计65%有效性</li>
                        <li><strong>心理声学原理</strong>: 采用A-weighting和Bark尺度分析</li>
                        <li><strong>安全标准</strong>: 基于WHO和相关医学研究的音量安全阈值</li>
                    </ul>
                </div>

                <div class="safety-warning">
                    <h4>🎯 使用建议</h4>
                    <h5>💡 一般使用原则</h5>
                    <ul>
                        <li><strong>音量控制</strong>: 成人≤60dB，婴幼儿≤50dB，距离≥2米</li>
                        <li><strong>使用时间</strong>: 建议睡前30分钟开始播放，入睡后可继续</li>
                        <li><strong>环境配置</strong>: 在安静的卧室环境中使用效果最佳</li>
                        <li><strong>个体差异</strong>: 根据个人偏好和反应调整使用方式</li>
                    </ul>

                    <h5>🚨 安全注意事项</h5>
                    <ul>
                        <li><strong>婴幼儿使用</strong>: 严格控制音量和距离，避免长时间连续使用</li>
                        <li><strong>听力保护</strong>: 定期检查听力，如有不适立即停止使用</li>
                        <li><strong>依赖性</strong>: 避免过度依赖，建议间歇性使用</li>
                        <li><strong>医疗咨询</strong>: 有听力问题或睡眠障碍者请咨询医生</li>
                    </ul>
                </div>

                <div class="scientific-evidence">
                    <h4>📊 推荐优先级</h4>
                    <p>基于科学数据和分析结果，推荐使用优先级：</p>
                    <ol>
                        <li><strong>粉噪音</strong> - 科学证据最强，82%研究显示有效</li>
                        <li><strong>棕噪音</strong> - 低频丰富，理论支持较好</li>
                        <li><strong>白噪音</strong> - 传统选择，但科学支持相对较弱</li>
                    </ol>
                </div>
            </div>
        </main>

        <footer class="report-footer">
            <p>© 2025 智能睡眠音频评估系统 - 基于科学研究的睡眠音频分析</p>
            <p><strong>报告生成</strong>: 智能睡眠音频评估与推荐系统 v2.0 (专业增强版)</p>
            <p><strong>技术支持</strong>: 基于librosa、scipy和numpy的专业音频分析</p>
            <p><strong>数据来源</strong>: 《白噪音对睡眠影响的科学分析报告》及相关研究文献</p>
        </footer>
    </div>

    <script>
        // 视图切换功能
        function toggleView(viewType) {
            const professionalElements = document.querySelectorAll('.professional-view');
            const simpleElements = document.querySelectorAll('.simple-view');
            const buttons = document.querySelectorAll('.toggle-btn');

            buttons.forEach(btn => btn.classList.remove('active'));

            if (viewType === 'professional') {
                professionalElements.forEach(el => el.style.display = 'block');
                simpleElements.forEach(el => el.style.display = 'none');
                document.querySelector('.toggle-btn[onclick*="professional"]').classList.add('active');
            } else {
                professionalElements.forEach(el => el.style.display = 'none');
                simpleElements.forEach(el => el.style.display = 'block');
                document.querySelector('.toggle-btn[onclick*="simple"]').classList.add('active');
            }
        }

        // 折叠面板功能
        document.addEventListener('DOMContentLoaded', function() {
            const collapsibles = document.querySelectorAll('.collapsible');

            collapsibles.forEach(function(collapsible) {
                collapsible.addEventListener('click', function() {
                    this.classList.toggle('active');
                    const content = this.nextElementSibling;

                    if (content.classList.contains('active')) {
                        content.classList.remove('active');
                        content.style.maxHeight = null;
                    } else {
                        content.classList.add('active');
                        content.style.maxHeight = content.scrollHeight + "px";
                    }
                });
            });
        });

        // 初始化专业视图
        document.addEventListener('DOMContentLoaded', function() {
            toggleView('professional');
        });
    </script>
</body>
</html>
