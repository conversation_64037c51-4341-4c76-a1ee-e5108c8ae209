#!/usr/bin/env python3
"""
插件系统测试用例
版本: 1.0
创建时间: 2025-06-26

测试插件系统的各项功能，包括：
1. 插件加载和注册
2. 插件实例化和配置验证
3. 报告生成功能
4. 错误处理和降级机制
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import json
import sys

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from plugin_system import (
    PluginManager, PluginRegistry, ReportGeneratorPlugin, AnalysisPlugin,
    PluginError, PluginLoadError, PluginValidationError
)


class MockReportPlugin(ReportGeneratorPlugin):
    """测试用的模拟报告插件"""
    
    def get_plugin_info(self):
        return {
            'name': 'MockReportPlugin',
            'version': '1.0',
            'description': '测试用模拟插件',
            'supported_formats': ['mock']
        }
    
    def validate_config(self, config):
        return True
    
    def get_supported_formats(self):
        return ['mock']
    
    def generate_report(self, results, config):
        return f"Mock report with {len(results)} results"


class MockAnalysisPlugin(AnalysisPlugin):
    """测试用的模拟分析插件"""
    
    def get_plugin_info(self):
        return {
            'name': 'MockAnalysisPlugin',
            'version': '1.0',
            'description': '测试用模拟分析插件',
            'analysis_type': 'mock'
        }
    
    def validate_config(self, config):
        return True
    
    def get_analysis_type(self):
        return 'mock'
    
    def analyze(self, audio_data, config):
        return {'mock_result': 'success'}


class TestPluginRegistry(unittest.TestCase):
    """测试插件注册表"""
    
    def setUp(self):
        self.registry = PluginRegistry()
    
    def test_register_plugin(self):
        """测试插件注册"""
        success = self.registry.register_plugin(MockReportPlugin, 'report_generators')
        self.assertTrue(success)
        
        # 检查插件是否已注册
        plugin_class = self.registry.get_plugin_class('report_generators', 'MockReportPlugin')
        self.assertEqual(plugin_class, MockReportPlugin)
    
    def test_list_plugins(self):
        """测试列出插件"""
        self.registry.register_plugin(MockReportPlugin, 'report_generators')
        self.registry.register_plugin(MockAnalysisPlugin, 'analyzers')
        
        plugins = self.registry.list_plugins()
        self.assertIn('report_generators', plugins)
        self.assertIn('analyzers', plugins)
        self.assertIn('MockReportPlugin', plugins['report_generators'])
        self.assertIn('MockAnalysisPlugin', plugins['analyzers'])
    
    def test_unregister_plugin(self):
        """测试注销插件"""
        self.registry.register_plugin(MockReportPlugin, 'report_generators')
        success = self.registry.unregister_plugin('report_generators', 'MockReportPlugin')
        self.assertTrue(success)
        
        plugin_class = self.registry.get_plugin_class('report_generators', 'MockReportPlugin')
        self.assertIsNone(plugin_class)


class TestPluginManager(unittest.TestCase):
    """测试插件管理器"""
    
    def setUp(self):
        # 创建临时插件目录
        self.temp_dir = tempfile.mkdtemp()
        self.plugin_dir = Path(self.temp_dir) / "plugins"
        self.plugin_dir.mkdir()
        
        # 创建测试插件文件
        self.create_test_plugin_file()
        
        # 创建插件管理器
        self.manager = PluginManager(str(self.plugin_dir), {'auto_load': False})
    
    def tearDown(self):
        # 清理临时目录
        shutil.rmtree(self.temp_dir)
    
    def create_test_plugin_file(self):
        """创建测试插件文件"""
        plugin_code = '''
from plugin_system import ReportGeneratorPlugin

class TestFilePlugin(ReportGeneratorPlugin):
    def get_plugin_info(self):
        return {
            'name': 'TestFilePlugin',
            'version': '1.0',
            'description': '从文件加载的测试插件'
        }
    
    def validate_config(self, config):
        return True
    
    def get_supported_formats(self):
        return ['test']
    
    def generate_report(self, results, config):
        return "Test file plugin report"
'''
        plugin_file = self.plugin_dir / "test_plugin.py"
        plugin_file.write_text(plugin_code)
    
    def test_load_plugins_from_file(self):
        """测试从文件加载插件"""
        results = self.manager.load_all_plugins()
        self.assertTrue(results.get('test_plugin.py', False))
        
        # 检查插件是否已注册
        plugins = self.manager.registry.list_plugins()
        self.assertIn('TestFilePlugin', plugins.get('report_generators', []))
    
    def test_create_plugin_instance(self):
        """测试创建插件实例"""
        # 先注册插件
        self.manager.registry.register_plugin(MockReportPlugin, 'report_generators')
        
        # 创建实例
        instance = self.manager.create_plugin_instance('report_generators', 'MockReportPlugin')
        self.assertIsNotNone(instance)
        self.assertIsInstance(instance, MockReportPlugin)
        self.assertTrue(instance.is_initialized)
    
    def test_get_plugin_instance(self):
        """测试获取插件实例"""
        self.manager.registry.register_plugin(MockReportPlugin, 'report_generators')
        
        # 第一次获取（创建）
        instance1 = self.manager.get_plugin_instance('report_generators', 'MockReportPlugin')
        self.assertIsNotNone(instance1)
        
        # 第二次获取（复用）
        instance2 = self.manager.get_plugin_instance('report_generators', 'MockReportPlugin')
        self.assertEqual(instance1, instance2)
    
    def test_list_available_plugins(self):
        """测试列出可用插件"""
        self.manager.registry.register_plugin(MockReportPlugin, 'report_generators')
        
        plugins_info = self.manager.list_available_plugins()
        self.assertIn('report_generators', plugins_info)
        
        report_plugins = plugins_info['report_generators']
        self.assertTrue(len(report_plugins) > 0)
        
        # 检查插件信息
        mock_plugin_info = next((p for p in report_plugins if p['name'] == 'MockReportPlugin'), None)
        self.assertIsNotNone(mock_plugin_info)
        self.assertEqual(mock_plugin_info['version'], '1.0')


class TestPluginIntegration(unittest.TestCase):
    """测试插件集成功能"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.plugin_dir = Path(self.temp_dir) / "plugins"
        self.plugin_dir.mkdir()
        self.manager = PluginManager(str(self.plugin_dir))
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_report_generation(self):
        """测试报告生成"""
        # 注册插件
        self.manager.registry.register_plugin(MockReportPlugin, 'report_generators')
        
        # 创建模拟结果
        mock_results = [{'file': 'test1.wav'}, {'file': 'test2.wav'}]
        
        # 获取插件实例并生成报告
        instance = self.manager.get_plugin_instance('report_generators', 'MockReportPlugin')
        report = instance.generate_report(mock_results, {})
        
        self.assertIn('Mock report', report)
        self.assertIn('2 results', report)
    
    def test_plugin_error_handling(self):
        """测试插件错误处理"""
        # 尝试获取不存在的插件
        instance = self.manager.get_plugin_instance('report_generators', 'NonExistentPlugin')
        self.assertIsNone(instance)
    
    def test_plugin_config_validation(self):
        """测试插件配置验证"""
        
        class StrictConfigPlugin(ReportGeneratorPlugin):
            def get_plugin_info(self):
                return {'name': 'StrictConfigPlugin', 'version': '1.0'}
            
            def validate_config(self, config):
                return 'required_param' in config
            
            def get_supported_formats(self):
                return ['strict']
            
            def generate_report(self, results, config):
                return "Strict report"
        
        self.manager.registry.register_plugin(StrictConfigPlugin, 'report_generators')
        
        # 无效配置应该失败
        instance = self.manager.create_plugin_instance('report_generators', 'StrictConfigPlugin', {})
        self.assertIsNone(instance)
        
        # 有效配置应该成功
        instance = self.manager.create_plugin_instance(
            'report_generators', 'StrictConfigPlugin', 
            {'required_param': 'value'}
        )
        self.assertIsNotNone(instance)


def run_plugin_system_tests():
    """运行插件系统测试"""
    print("🧪 开始插件系统测试...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试用例
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestPluginRegistry))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestPluginManager))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestPluginIntegration))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
    else:
        print(f"❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_plugin_system_tests()
    sys.exit(0 if success else 1)
