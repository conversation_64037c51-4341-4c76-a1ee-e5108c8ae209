# 🔌 智能睡眠音频评估系统 - 插件化系统操作指南

## 📋 指南概览

**文档版本**: v2.0  
**适用系统**: 智能睡眠音频评估系统 v2.0 (插件化版本)  
**更新时间**: 2025年06月26日 12:50:00  
**目标用户**: 系统用户、开发者、研究人员

---

## 🚀 快速开始

### 1. 系统概览
插件化的智能睡眠音频评估系统支持多种报告格式和扩展功能，通过插件机制实现灵活的功能扩展。

### 2. 基本命令结构
```bash
python3 run_sleep_audio_analysis.py [输入文件/目录] [选项] [插件参数]
```

---

## 🔍 插件管理操作

### 1. 列出可用插件

#### 基本命令
```bash
python3 run_sleep_audio_analysis.py --list-plugins
```

#### 输出示例
```
🔌 可用插件列表:
==================================================

📂 Report Generators:
  ✅ CSVReportGenerator (v1.0)
     生成CSV格式的睡眠音频分析报告，便于数据分析

  ❌ ExcelReportGenerator (v1.0)
     生成Excel格式的睡眠音频分析报告
     📦 需要依赖: openpyxl

  ✅ HTMLReportGenerator (v1.0)
     生成美观的HTML格式睡眠音频分析报告

  ❌ PDFReportGenerator (v1.0)
     生成PDF格式的睡眠音频分析报告
     📦 需要依赖: reportlab

📂 Analyzers: 无可用插件
💡 使用方法: --plugin <插件名称>
```

#### 状态说明
- ✅ **可用**: 插件已就绪，可直接使用
- ❌ **需依赖**: 插件需要安装额外依赖库
- 📦 **依赖信息**: 显示所需的Python包

### 2. 检查插件依赖

#### 安装PDF插件依赖
```bash
pip install reportlab
```

#### 安装Excel插件依赖
```bash
pip install openpyxl
```

#### 验证安装
```bash
python3 run_sleep_audio_analysis.py --list-plugins
```

---

## 📊 使用插件生成报告

### 1. HTML报告生成

#### 单文件分析
```bash
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin HTMLReportGenerator --auto-name
```

#### 批量分析
```bash
python3 run_sleep_audio_analysis.py noisekun --all --plugin HTMLReportGenerator --auto-name
```

#### 指定输出文件
```bash
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin HTMLReportGenerator --output my_report.html
```

### 2. CSV数据导出

#### 基本用法
```bash
python3 run_sleep_audio_analysis.py noisekun --all --plugin CSVReportGenerator --auto-name
```

#### 详细数据导出
```bash
python3 run_sleep_audio_analysis.py noisekun --all --plugin CSVReportGenerator --output detailed_analysis.csv --detailed
```

### 3. PDF报告生成 (需要依赖)

#### 安装依赖
```bash
pip install reportlab
```

#### 生成PDF报告
```bash
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin PDFReportGenerator --auto-name
```

### 4. Excel报告生成 (需要依赖)

#### 安装依赖
```bash
pip install openpyxl
```

#### 生成Excel报告
```bash
python3 run_sleep_audio_analysis.py noisekun --all --plugin ExcelReportGenerator --auto-name
```

---

## ⚙️ 插件配置

### 1. 创建配置文件

#### 配置文件示例 (plugin_config.json)
```json
{
    "HTMLReportGenerator": {
        "title": "自定义睡眠音频分析报告",
        "template_style": "modern",
        "include_charts": true,
        "color_scheme": "blue"
    },
    "PDFReportGenerator": {
        "page_size": "A4",
        "font_size": 12,
        "include_logo": true
    },
    "CSVReportGenerator": {
        "include_detailed": true,
        "delimiter": ",",
        "encoding": "utf-8"
    }
}
```

### 2. 使用配置文件

```bash
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin HTMLReportGenerator --plugin-config plugin_config.json
```

### 3. 配置参数说明

#### HTMLReportGenerator配置
- `title`: 报告标题
- `template_style`: 模板样式 (modern/simple)
- `include_charts`: 是否包含图表
- `color_scheme`: 颜色主题

#### PDFReportGenerator配置
- `page_size`: 页面大小 (A4/Letter)
- `font_size`: 字体大小
- `include_logo`: 是否包含Logo

#### CSVReportGenerator配置
- `include_detailed`: 是否包含详细参数
- `delimiter`: 分隔符
- `encoding`: 文件编码

---

## 🛠️ 高级用法

### 1. 组合使用多个插件

#### 生成多种格式报告
```bash
# 生成HTML报告
python3 run_sleep_audio_analysis.py noisekun --all --plugin HTMLReportGenerator --output report.html

# 生成CSV数据
python3 run_sleep_audio_analysis.py noisekun --all --plugin CSVReportGenerator --output data.csv

# 生成PDF报告
python3 run_sleep_audio_analysis.py noisekun --all --plugin PDFReportGenerator --output report.pdf
```

### 2. 批处理脚本示例

#### 创建批处理脚本 (batch_analysis.sh)
```bash
#!/bin/bash

INPUT_DIR="noisekun"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "开始批量分析..."

# HTML报告
python3 run_sleep_audio_analysis.py "$INPUT_DIR" --all --plugin HTMLReportGenerator --output "analysis_${TIMESTAMP}.html"

# CSV数据
python3 run_sleep_audio_analysis.py "$INPUT_DIR" --all --plugin CSVReportGenerator --output "data_${TIMESTAMP}.csv"

# PDF报告 (如果依赖已安装)
if python3 -c "import reportlab" 2>/dev/null; then
    python3 run_sleep_audio_analysis.py "$INPUT_DIR" --all --plugin PDFReportGenerator --output "report_${TIMESTAMP}.pdf"
fi

echo "分析完成！"
```

### 3. 错误处理和降级

#### 插件失败时的降级机制
```bash
# 如果插件失败，系统会自动使用内置生成器
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin NonExistentPlugin --format markdown
```

---

## 🔧 故障排除

### 1. 常见问题

#### 问题1: 插件未找到
```
❌ 未找到插件: MyPlugin
```
**解决方案**:
- 检查插件名称拼写
- 使用 `--list-plugins` 查看可用插件
- 确认插件文件在plugins目录中

#### 问题2: 依赖缺失
```
❌ PDF插件需要reportlab库支持
```
**解决方案**:
```bash
pip install reportlab
```

#### 问题3: 配置文件错误
```
⚠️ 加载插件配置失败
```
**解决方案**:
- 检查JSON格式是否正确
- 验证文件路径是否存在
- 确认文件编码为UTF-8

### 2. 调试模式

#### 启用详细日志
```bash
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin HTMLReportGenerator --verbose
```

#### 检查插件状态
```bash
python3 -c "
from plugin_system import get_plugin_manager
manager = get_plugin_manager()
print(manager.list_available_plugins())
"
```

---

## 📈 性能优化

### 1. 批量处理优化

#### 大文件批处理
```bash
# 使用CSV插件处理大量文件（性能最佳）
python3 run_sleep_audio_analysis.py large_dataset --all --plugin CSVReportGenerator --auto-name
```

#### 并行处理（未来功能）
```bash
# 计划中的并行处理功能
python3 run_sleep_audio_analysis.py dataset --all --plugin HTMLReportGenerator --parallel 4
```

### 2. 内存管理

#### 处理大型数据集
- 使用CSV插件进行大批量分析
- 避免同时生成多种格式报告
- 定期清理临时文件

---

## 🎯 最佳实践

### 1. 报告格式选择指南

| 用途 | 推荐插件 | 优势 | 适用场景 |
|------|----------|------|----------|
| **数据分析** | CSVReportGenerator | 轻量、快速、易处理 | 科学研究、统计分析 |
| **展示汇报** | HTMLReportGenerator | 美观、交互、易分享 | 项目汇报、在线展示 |
| **正式文档** | PDFReportGenerator | 专业、打印友好 | 正式报告、存档 |
| **数据处理** | ExcelReportGenerator | 格式化、公式支持 | 业务分析、数据处理 |

### 2. 工作流程建议

#### 标准分析流程
1. **探索阶段**: 使用HTML插件快速查看结果
2. **数据分析**: 使用CSV插件导出详细数据
3. **报告制作**: 使用PDF插件生成正式报告
4. **数据存档**: 保存所有格式用于后续参考

#### 批量处理流程
1. **预检查**: 使用 `--list-plugins` 确认插件可用性
2. **配置准备**: 创建适当的配置文件
3. **批量执行**: 使用脚本自动化处理
4. **结果验证**: 检查生成的报告质量

---

**📝 指南更新时间**: 2025年06月26日 12:50:00  
**🔧 技术支持**: 智能睡眠音频评估系统 v2.0  
**📊 文档状态**: 完整操作指南
