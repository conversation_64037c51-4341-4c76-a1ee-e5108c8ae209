# 🛠️ 自定义插件开发教程

## 📋 教程概览

**教程目标**: 从零开始创建自定义睡眠音频分析插件  
**难度等级**: 中级  
**预计时间**: 30-60分钟  
**适用对象**: Python开发者、系统扩展需求用户

---

## 🎯 学习目标

完成本教程后，您将能够：
- ✅ 理解插件系统的工作原理
- ✅ 创建自定义报告生成器插件
- ✅ 实现插件配置和验证
- ✅ 测试和调试插件功能
- ✅ 部署和使用自定义插件

---

## 🚀 实战项目：创建JSON增强报告插件

### 项目描述
我们将创建一个增强版JSON报告插件，它不仅包含基本分析结果，还添加了统计信息、图表数据和导出时间戳。

### 最终效果预览
```json
{
  "report_metadata": {
    "generated_at": "2025-06-26T12:55:00",
    "system_version": "2.0",
    "plugin_version": "1.0",
    "total_files": 3
  },
  "summary_statistics": {
    "average_score": 72.5,
    "highest_score": 89.7,
    "lowest_score": 61.0,
    "excellent_files": 1,
    "good_files": 2
  },
  "detailed_results": [...],
  "chart_data": {...}
}
```

---

## 📝 步骤1：创建插件文件

### 1.1 创建插件文件
```bash
# 在plugins目录下创建新插件文件
touch plugins/enhanced_json_generator.py
```

### 1.2 基础插件结构
```python
# plugins/enhanced_json_generator.py
"""
增强版JSON报告生成器插件
版本: 1.0
创建时间: 2025-06-26

功能特性:
- 生成结构化JSON报告
- 包含统计摘要信息
- 添加图表数据支持
- 时间戳和元数据
"""

import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# 导入插件基类
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin


class EnhancedJSONGenerator(ReportGeneratorPlugin):
    """增强版JSON报告生成器插件"""
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': 'EnhancedJSONGenerator',
            'version': '1.0',
            'description': '生成增强版JSON格式的睡眠音频分析报告，包含统计信息和图表数据',
            'author': '智能睡眠音频评估系统',
            'supported_formats': ['json', 'enhanced_json'],
            'dependencies': []
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置参数"""
        # 检查必需参数
        if 'include_statistics' in config:
            if not isinstance(config['include_statistics'], bool):
                self.logger.error("include_statistics 必须是布尔值")
                return False
        
        if 'include_chart_data' in config:
            if not isinstance(config['include_chart_data'], bool):
                self.logger.error("include_chart_data 必须是布尔值")
                return False
        
        return True
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的格式"""
        return ['json', 'enhanced_json']
    
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """生成增强版JSON报告"""
        try:
            # 获取配置参数
            include_statistics = config.get('include_statistics', True)
            include_chart_data = config.get('include_chart_data', True)
            include_metadata = config.get('include_metadata', True)
            pretty_print = config.get('pretty_print', True)
            
            # 构建报告数据
            report_data = {}
            
            # 添加元数据
            if include_metadata:
                report_data['report_metadata'] = self._generate_metadata(results, config)
            
            # 添加统计信息
            if include_statistics:
                report_data['summary_statistics'] = self._generate_statistics(results)
            
            # 添加详细结果
            report_data['detailed_results'] = self._convert_results_to_dict(results)
            
            # 添加图表数据
            if include_chart_data:
                report_data['chart_data'] = self._generate_chart_data(results)
            
            # 转换为JSON
            if pretty_print:
                json_content = json.dumps(report_data, ensure_ascii=False, indent=2)
            else:
                json_content = json.dumps(report_data, ensure_ascii=False)
            
            # 保存到文件（如果指定了输出路径）
            output_path = config.get('output_path')
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(json_content)
                return f"增强版JSON报告已保存到: {output_path}"
            
            return json_content
            
        except Exception as e:
            self.logger.error(f"增强版JSON报告生成失败: {e}")
            return f"错误: {str(e)}"
```

---

## 🔧 步骤2：实现核心功能方法

### 2.1 添加辅助方法
```python
    def _generate_metadata(self, results: List[Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """生成报告元数据"""
        return {
            'generated_at': datetime.now().isoformat(),
            'system_version': '2.0',
            'plugin_name': 'EnhancedJSONGenerator',
            'plugin_version': '1.0',
            'total_files': len(results),
            'config_used': {k: v for k, v in config.items() if k != 'output_path'}
        }
    
    def _generate_statistics(self, results: List[Any]) -> Dict[str, Any]:
        """生成统计摘要信息"""
        if not results:
            return {}
        
        scores = [r.sleep_suitability.overall_score for r in results]
        
        return {
            'total_files': len(results),
            'average_score': round(sum(scores) / len(scores), 1),
            'highest_score': max(scores),
            'lowest_score': min(scores),
            'score_range': round(max(scores) - min(scores), 1),
            'excellent_files': sum(1 for score in scores if score >= 80),
            'good_files': sum(1 for score in scores if 60 <= score < 80),
            'fair_files': sum(1 for score in scores if 40 <= score < 60),
            'poor_files': sum(1 for score in scores if score < 40),
            'noise_types': self._count_noise_types(results),
            'safety_levels': self._count_safety_levels(results)
        }
    
    def _count_noise_types(self, results: List[Any]) -> Dict[str, int]:
        """统计噪音类型分布"""
        noise_counts = {}
        for result in results:
            noise_type = result.audio_features.noise_type.value
            noise_counts[noise_type] = noise_counts.get(noise_type, 0) + 1
        return noise_counts
    
    def _count_safety_levels(self, results: List[Any]) -> Dict[str, int]:
        """统计安全等级分布"""
        safety_counts = {}
        for result in results:
            safety_level = result.safety_assessment.overall_safety.value
            safety_counts[safety_level] = safety_counts.get(safety_level, 0) + 1
        return safety_counts
    
    def _convert_results_to_dict(self, results: List[Any]) -> List[Dict[str, Any]]:
        """将分析结果转换为字典格式"""
        converted_results = []
        
        for result in results:
            result_dict = {
                'file_info': {
                    'filename': Path(result.audio_file).name,
                    'filepath': result.audio_file,
                    'analysis_timestamp': result.analysis_timestamp
                },
                'sleep_suitability': {
                    'overall_score': result.sleep_suitability.overall_score,
                    'effectiveness_prediction': result.sleep_suitability.effectiveness_prediction,
                    'comfort_level': result.sleep_suitability.comfort_level,
                    'interference_risk': result.sleep_suitability.interference_risk
                },
                'audio_features': {
                    'noise_type': result.audio_features.noise_type.value,
                    'audio_source': result.audio_features.audio_source.value,
                    'spectral_slope': result.audio_features.spectral_slope,
                    'loudness_stability': result.audio_features.loudness_stability,
                    'tonal_ratio': result.audio_features.tonal_ratio,
                    'dynamic_range_db': result.audio_features.dynamic_range_db,
                    'sound_labels': result.audio_features.sound_labels
                },
                'safety_assessment': {
                    'overall_safety': result.safety_assessment.overall_safety.value,
                    'volume_safety': result.safety_assessment.volume_safety.value,
                    'content_safety': result.safety_assessment.content_safety.value,
                    'recommended_volume_db': result.safety_assessment.recommended_volume_db,
                    'recommended_distance_cm': result.safety_assessment.recommended_distance_cm,
                    'max_duration_hours': result.safety_assessment.max_duration_hours,
                    'safety_warnings': result.safety_assessment.safety_warnings
                },
                'overall_recommendation': result.overall_recommendation
            }
            converted_results.append(result_dict)
        
        return converted_results
    
    def _generate_chart_data(self, results: List[Any]) -> Dict[str, Any]:
        """生成图表数据"""
        if not results:
            return {}
        
        # 得分分布数据
        scores = [r.sleep_suitability.overall_score for r in results]
        filenames = [Path(r.audio_file).name for r in results]
        
        # 噪音类型分布
        noise_types = self._count_noise_types(results)
        
        # 安全等级分布
        safety_levels = self._count_safety_levels(results)
        
        return {
            'score_distribution': {
                'labels': filenames,
                'scores': scores,
                'colors': [self._get_score_color(score) for score in scores]
            },
            'noise_type_distribution': {
                'labels': list(noise_types.keys()),
                'values': list(noise_types.values())
            },
            'safety_level_distribution': {
                'labels': list(safety_levels.keys()),
                'values': list(safety_levels.values())
            },
            'score_histogram': {
                'excellent': sum(1 for score in scores if score >= 80),
                'good': sum(1 for score in scores if 60 <= score < 80),
                'fair': sum(1 for score in scores if 40 <= score < 60),
                'poor': sum(1 for score in scores if score < 40)
            }
        }
    
    def _get_score_color(self, score: float) -> str:
        """根据得分返回对应颜色"""
        if score >= 80:
            return '#4CAF50'  # 绿色
        elif score >= 60:
            return '#2196F3'  # 蓝色
        elif score >= 40:
            return '#FF9800'  # 橙色
        else:
            return '#F44336'  # 红色
```

---

## 🧪 步骤3：测试插件

### 3.1 创建测试脚本
```python
# test_enhanced_json_plugin.py
import unittest
import json
import tempfile
from pathlib import Path

# 导入插件
from plugins.enhanced_json_generator import EnhancedJSONGenerator

class TestEnhancedJSONGenerator(unittest.TestCase):
    
    def setUp(self):
        self.plugin = EnhancedJSONGenerator()
        # 创建模拟结果数据
        self.mock_results = self._create_mock_results()
    
    def test_plugin_info(self):
        """测试插件信息"""
        info = self.plugin.get_plugin_info()
        self.assertEqual(info['name'], 'EnhancedJSONGenerator')
        self.assertEqual(info['version'], '1.0')
        self.assertIn('json', info['supported_formats'])
    
    def test_config_validation(self):
        """测试配置验证"""
        valid_config = {
            'include_statistics': True,
            'include_chart_data': True,
            'pretty_print': True
        }
        self.assertTrue(self.plugin.validate_config(valid_config))
        
        invalid_config = {
            'include_statistics': 'invalid'  # 应该是布尔值
        }
        self.assertFalse(self.plugin.validate_config(invalid_config))
    
    def test_report_generation(self):
        """测试报告生成"""
        config = {
            'include_statistics': True,
            'include_chart_data': True,
            'pretty_print': True
        }
        
        result = self.plugin.generate_report(self.mock_results, config)
        
        # 验证返回的是有效JSON
        try:
            data = json.loads(result)
            self.assertIn('report_metadata', data)
            self.assertIn('summary_statistics', data)
            self.assertIn('detailed_results', data)
            self.assertIn('chart_data', data)
        except json.JSONDecodeError:
            self.fail("生成的报告不是有效的JSON格式")
    
    def _create_mock_results(self):
        """创建模拟分析结果"""
        # 这里需要根据实际的结果对象结构创建模拟数据
        # 简化示例
        class MockResult:
            def __init__(self, filename, score):
                self.audio_file = filename
                self.analysis_timestamp = "2025-06-26T12:55:00"
                self.sleep_suitability = MockSleepSuitability(score)
                self.audio_features = MockAudioFeatures()
                self.safety_assessment = MockSafetyAssessment()
                self.overall_recommendation = "测试推荐"
        
        # 创建模拟对象...
        return [MockResult("test1.wav", 85), MockResult("test2.wav", 70)]

if __name__ == '__main__':
    unittest.main()
```

### 3.2 运行测试
```bash
python3 test_enhanced_json_plugin.py
```

---

## 🚀 步骤4：使用插件

### 4.1 验证插件加载
```bash
python3 run_sleep_audio_analysis.py --list-plugins
```

### 4.2 使用插件生成报告
```bash
# 基本使用
python3 run_sleep_audio_analysis.py noisekun/waves.ogm --plugin EnhancedJSONGenerator --auto-name

# 使用配置文件
echo '{
  "EnhancedJSONGenerator": {
    "include_statistics": true,
    "include_chart_data": true,
    "pretty_print": true
  }
}' > enhanced_json_config.json

python3 run_sleep_audio_analysis.py noisekun --all --plugin EnhancedJSONGenerator --plugin-config enhanced_json_config.json --output enhanced_report.json
```

---

## 📦 步骤5：插件打包和分发

### 5.1 创建插件包
```bash
mkdir enhanced_json_plugin_package
cp plugins/enhanced_json_generator.py enhanced_json_plugin_package/
cp test_enhanced_json_plugin.py enhanced_json_plugin_package/
```

### 5.2 创建安装说明
```markdown
# Enhanced JSON Generator Plugin

## 安装方法
1. 将 enhanced_json_generator.py 复制到 plugins/ 目录
2. 重启系统或重新加载插件

## 使用方法
```bash
python3 run_sleep_audio_analysis.py input.wav --plugin EnhancedJSONGenerator --auto-name
```

## 配置选项
- include_statistics: 是否包含统计信息
- include_chart_data: 是否包含图表数据
- pretty_print: 是否格式化输出
```

---

## 🎯 进阶扩展

### 扩展1：添加数据导出功能
```python
def export_to_csv(self, results: List[Any], output_path: str):
    """导出统计数据到CSV"""
    import csv
    
    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['文件名', '得分', '噪音类型', '安全等级'])
        
        for result in results:
            writer.writerow([
                Path(result.audio_file).name,
                result.sleep_suitability.overall_score,
                result.audio_features.noise_type.value,
                result.safety_assessment.overall_safety.value
            ])
```

### 扩展2：添加数据可视化
```python
def generate_charts(self, results: List[Any], output_dir: str):
    """生成图表文件"""
    try:
        import matplotlib.pyplot as plt
        
        scores = [r.sleep_suitability.overall_score for r in results]
        filenames = [Path(r.audio_file).name for r in results]
        
        plt.figure(figsize=(10, 6))
        plt.bar(filenames, scores)
        plt.title('睡眠适用性得分对比')
        plt.ylabel('得分')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f"{output_dir}/score_comparison.png")
        plt.close()
        
    except ImportError:
        self.logger.warning("matplotlib未安装，跳过图表生成")
```

---

## 📚 总结和最佳实践

### ✅ 开发要点
1. **继承正确的基类**: ReportGeneratorPlugin 或 AnalysisPlugin
2. **实现必需方法**: get_plugin_info(), validate_config(), 核心功能方法
3. **错误处理**: 完善的异常处理和日志记录
4. **配置验证**: 严格的参数类型和值验证
5. **文档完整**: 详细的docstring和使用说明

### 🎯 性能优化
1. **延迟导入**: 只在需要时导入重型库
2. **内存管理**: 及时释放大对象
3. **缓存机制**: 缓存计算结果
4. **批量处理**: 优化大数据集处理

### 🔒 安全考虑
1. **输入验证**: 验证所有外部输入
2. **路径安全**: 防止路径遍历攻击
3. **资源限制**: 限制内存和CPU使用
4. **权限控制**: 最小权限原则

---

**🎉 恭喜！您已经成功创建了第一个自定义插件！**

通过这个教程，您学会了插件开发的完整流程。现在您可以根据自己的需求创建更多有趣的插件了！

---

**📝 教程完成时间**: 2025年06月26日 13:00:00  
**🔧 技术支持**: 智能睡眠音频评估系统 v2.0  
**📊 教程状态**: 完整实战教程
