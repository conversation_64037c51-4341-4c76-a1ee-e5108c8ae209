"""
智能睡眠音频评估系统 - 插件系统架构
版本: 2.0
创建时间: 2025-06-26

本模块定义了插件系统的核心架构，包括：
1. 插件基类接口定义
2. 插件管理器实现
3. 插件注册和发现机制
4. 插件生命周期管理
"""

import importlib
import importlib.util
import inspect
import logging
import threading
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union
import json
import traceback


class PluginError(Exception):
    """插件系统异常基类"""
    pass


class PluginLoadError(PluginError):
    """插件加载异常"""
    pass


class PluginValidationError(PluginError):
    """插件验证异常"""
    pass


class PluginInterface(ABC):
    """插件接口基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化插件
        
        Args:
            config: 插件配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self._initialized = False
    
    @abstractmethod
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件信息
        
        Returns:
            包含插件名称、版本、描述等信息的字典
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        验证配置的有效性
        
        Args:
            config: 配置参数
            
        Returns:
            配置是否有效
        """
        pass
    
    def initialize(self) -> bool:
        """
        初始化插件
        
        Returns:
            初始化是否成功
        """
        try:
            self._initialized = True
            self.logger.info(f"插件 {self.__class__.__name__} 初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"插件 {self.__class__.__name__} 初始化失败: {e}")
            return False
    
    def cleanup(self):
        """清理插件资源"""
        self._initialized = False
        self.logger.info(f"插件 {self.__class__.__name__} 已清理")
    
    @property
    def is_initialized(self) -> bool:
        """检查插件是否已初始化"""
        return self._initialized


class ReportGeneratorPlugin(PluginInterface):
    """报告生成器插件基类"""
    
    @abstractmethod
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """
        生成报告的核心方法
        
        Args:
            results: 分析结果列表
            config: 生成配置
            
        Returns:
            生成的报告内容
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        返回支持的输出格式列表
        
        Returns:
            支持的格式列表，如 ['html', 'pdf']
        """
        pass
    
    def get_file_extension(self, format_name: str) -> str:
        """
        获取指定格式的文件扩展名
        
        Args:
            format_name: 格式名称
            
        Returns:
            文件扩展名
        """
        format_extensions = {
            'html': '.html',
            'pdf': '.pdf',
            'docx': '.docx',
            'xlsx': '.xlsx',
            'csv': '.csv'
        }
        return format_extensions.get(format_name.lower(), '.txt')


class AnalysisPlugin(PluginInterface):
    """分析插件基类"""
    
    @abstractmethod
    def analyze(self, audio_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行分析的核心方法
        
        Args:
            audio_data: 音频数据
            config: 分析配置
            
        Returns:
            分析结果字典
        """
        pass
    
    @abstractmethod
    def get_analysis_type(self) -> str:
        """
        返回分析类型
        
        Returns:
            分析类型标识符
        """
        pass
    
    def get_required_dependencies(self) -> List[str]:
        """
        获取插件所需的依赖库
        
        Returns:
            依赖库列表
        """
        return []


class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        self._plugins: Dict[str, Dict[str, Type[PluginInterface]]] = {
            'report_generators': {},
            'analyzers': {}
        }
        self._lock = threading.RLock()
    
    def register_plugin(self, plugin_class: Type[PluginInterface], plugin_type: str) -> bool:
        """
        注册插件
        
        Args:
            plugin_class: 插件类
            plugin_type: 插件类型 ('report_generators' 或 'analyzers')
            
        Returns:
            注册是否成功
        """
        with self._lock:
            try:
                if plugin_type not in self._plugins:
                    self._plugins[plugin_type] = {}
                
                plugin_name = plugin_class.__name__
                self._plugins[plugin_type][plugin_name] = plugin_class
                
                logging.info(f"插件已注册: {plugin_type}.{plugin_name}")
                return True
                
            except Exception as e:
                logging.error(f"插件注册失败: {e}")
                return False
    
    def get_plugin_class(self, plugin_type: str, plugin_name: str) -> Optional[Type[PluginInterface]]:
        """
        获取插件类
        
        Args:
            plugin_type: 插件类型
            plugin_name: 插件名称
            
        Returns:
            插件类或None
        """
        with self._lock:
            return self._plugins.get(plugin_type, {}).get(plugin_name)
    
    def list_plugins(self, plugin_type: Optional[str] = None) -> Dict[str, List[str]]:
        """
        列出可用插件
        
        Args:
            plugin_type: 插件类型，None表示列出所有类型
            
        Returns:
            插件列表字典
        """
        with self._lock:
            if plugin_type:
                return {plugin_type: list(self._plugins.get(plugin_type, {}).keys())}
            return {ptype: list(plugins.keys()) for ptype, plugins in self._plugins.items()}
    
    def unregister_plugin(self, plugin_type: str, plugin_name: str) -> bool:
        """
        注销插件
        
        Args:
            plugin_type: 插件类型
            plugin_name: 插件名称
            
        Returns:
            注销是否成功
        """
        with self._lock:
            try:
                if plugin_type in self._plugins and plugin_name in self._plugins[plugin_type]:
                    del self._plugins[plugin_type][plugin_name]
                    logging.info(f"插件已注销: {plugin_type}.{plugin_name}")
                    return True
                return False
            except Exception as e:
                logging.error(f"插件注销失败: {e}")
                return False


class PluginManager:
    """插件管理器"""

    def __init__(self, plugin_dir: str = "plugins", config: Optional[Dict[str, Any]] = None):
        """
        初始化插件管理器

        Args:
            plugin_dir: 插件目录路径
            config: 插件系统配置
        """
        self.plugin_dir = Path(plugin_dir)
        self.config = config or {}
        self.registry = plugin_registry
        self.loaded_plugins: Dict[str, PluginInterface] = {}
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()

        # 确保插件目录存在
        self.plugin_dir.mkdir(parents=True, exist_ok=True)

        # 创建__init__.py文件使其成为Python包
        init_file = self.plugin_dir / "__init__.py"
        if not init_file.exists():
            init_file.write_text("# 插件目录\n")

        # 自动加载插件
        if self.config.get('auto_load', True):
            self.load_all_plugins()

    def load_all_plugins(self) -> Dict[str, bool]:
        """
        加载所有插件

        Returns:
            加载结果字典，键为插件文件名，值为加载是否成功
        """
        results = {}

        if not self.plugin_dir.exists():
            self.logger.warning(f"插件目录不存在: {self.plugin_dir}")
            return results

        # 查找所有Python文件
        for plugin_file in self.plugin_dir.glob("*.py"):
            if plugin_file.name.startswith('__'):
                continue

            try:
                success = self.load_plugin_from_file(plugin_file)
                results[plugin_file.name] = success

            except Exception as e:
                self.logger.error(f"加载插件文件失败 {plugin_file}: {e}")
                results[plugin_file.name] = False

        self.logger.info(f"插件加载完成，成功: {sum(results.values())}, 失败: {len(results) - sum(results.values())}")
        return results

    def load_plugin_from_file(self, plugin_file: Path) -> bool:
        """
        从文件加载插件

        Args:
            plugin_file: 插件文件路径

        Returns:
            加载是否成功
        """
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                plugin_file.stem, plugin_file
            )
            if spec is None or spec.loader is None:
                raise PluginLoadError(f"无法创建模块规范: {plugin_file}")

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 查找插件类
            plugin_classes_found = 0
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if self._is_valid_plugin_class(obj):
                    plugin_type = self._determine_plugin_type(obj)
                    if plugin_type:
                        self.registry.register_plugin(obj, plugin_type)
                        plugin_classes_found += 1
                        self.logger.info(f"发现插件类: {name} (类型: {plugin_type})")

            if plugin_classes_found == 0:
                self.logger.warning(f"文件中未找到有效的插件类: {plugin_file}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"加载插件文件失败 {plugin_file}: {e}")
            self.logger.debug(traceback.format_exc())
            return False

    def _is_valid_plugin_class(self, cls: Type) -> bool:
        """
        检查类是否为有效的插件类

        Args:
            cls: 要检查的类

        Returns:
            是否为有效插件类
        """
        return (
            inspect.isclass(cls) and
            issubclass(cls, PluginInterface) and
            cls not in (PluginInterface, ReportGeneratorPlugin, AnalysisPlugin) and
            not inspect.isabstract(cls)
        )

    def _determine_plugin_type(self, cls: Type[PluginInterface]) -> Optional[str]:
        """
        确定插件类型

        Args:
            cls: 插件类

        Returns:
            插件类型字符串或None
        """
        if issubclass(cls, ReportGeneratorPlugin):
            return 'report_generators'
        elif issubclass(cls, AnalysisPlugin):
            return 'analyzers'
        return None


    def create_plugin_instance(self, plugin_type: str, plugin_name: str,
                              config: Optional[Dict[str, Any]] = None) -> Optional[PluginInterface]:
        """
        创建插件实例

        Args:
            plugin_type: 插件类型
            plugin_name: 插件名称
            config: 插件配置

        Returns:
            插件实例或None
        """
        with self._lock:
            try:
                plugin_class = self.registry.get_plugin_class(plugin_type, plugin_name)
                if plugin_class is None:
                    self.logger.error(f"未找到插件: {plugin_type}.{plugin_name}")
                    return None

                # 合并配置
                plugin_config = self.config.get('plugins', {}).get(plugin_name, {})
                if config:
                    plugin_config.update(config)

                # 创建实例
                instance = plugin_class(plugin_config)

                # 验证配置
                if not instance.validate_config(plugin_config):
                    raise PluginValidationError(f"插件配置验证失败: {plugin_name}")

                # 初始化插件
                if not instance.initialize():
                    raise PluginLoadError(f"插件初始化失败: {plugin_name}")

                # 缓存实例
                instance_key = f"{plugin_type}.{plugin_name}"
                self.loaded_plugins[instance_key] = instance

                self.logger.info(f"插件实例创建成功: {instance_key}")
                return instance

            except Exception as e:
                self.logger.error(f"创建插件实例失败 {plugin_type}.{plugin_name}: {e}")
                return None

    def get_plugin_instance(self, plugin_type: str, plugin_name: str) -> Optional[PluginInterface]:
        """
        获取插件实例（如果不存在则创建）

        Args:
            plugin_type: 插件类型
            plugin_name: 插件名称

        Returns:
            插件实例或None
        """
        instance_key = f"{plugin_type}.{plugin_name}"

        with self._lock:
            # 检查是否已有实例
            if instance_key in self.loaded_plugins:
                instance = self.loaded_plugins[instance_key]
                if instance.is_initialized:
                    return instance
                else:
                    # 实例已失效，移除并重新创建
                    del self.loaded_plugins[instance_key]

            # 创建新实例
            return self.create_plugin_instance(plugin_type, plugin_name)

    def list_available_plugins(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        列出所有可用插件及其信息

        Returns:
            插件信息字典
        """
        result = {}

        for plugin_type, plugins in self.registry.list_plugins().items():
            result[plugin_type] = []

            for plugin_name in plugins:
                try:
                    # 尝试创建临时实例获取信息
                    plugin_class = self.registry.get_plugin_class(plugin_type, plugin_name)
                    if plugin_class:
                        temp_instance = plugin_class({})
                        plugin_info = temp_instance.get_plugin_info()
                        plugin_info['name'] = plugin_name
                        plugin_info['type'] = plugin_type
                        result[plugin_type].append(plugin_info)

                except Exception as e:
                    self.logger.warning(f"获取插件信息失败 {plugin_type}.{plugin_name}: {e}")
                    result[plugin_type].append({
                        'name': plugin_name,
                        'type': plugin_type,
                        'error': str(e)
                    })

        return result

    def cleanup_all_plugins(self):
        """清理所有插件实例"""
        with self._lock:
            for instance_key, instance in self.loaded_plugins.items():
                try:
                    instance.cleanup()
                    self.logger.info(f"插件已清理: {instance_key}")
                except Exception as e:
                    self.logger.error(f"清理插件失败 {instance_key}: {e}")

            self.loaded_plugins.clear()

    def reload_plugins(self) -> Dict[str, bool]:
        """
        重新加载所有插件

        Returns:
            重新加载结果
        """
        self.logger.info("开始重新加载插件...")

        # 清理现有插件
        self.cleanup_all_plugins()

        # 清空注册表
        self.registry._plugins = {
            'report_generators': {},
            'analyzers': {}
        }

        # 重新加载
        return self.load_all_plugins()

    def __del__(self):
        """析构函数，清理资源"""
        try:
            self.cleanup_all_plugins()
        except:
            pass


# 全局插件注册表实例
plugin_registry = PluginRegistry()

# 全局插件管理器实例
_plugin_manager = None

def get_plugin_manager(plugin_dir: str = "plugins",
                      config: Optional[Dict[str, Any]] = None) -> PluginManager:
    """
    获取全局插件管理器实例

    Args:
        plugin_dir: 插件目录
        config: 配置参数

    Returns:
        插件管理器实例
    """
    global _plugin_manager
    if _plugin_manager is None:
        _plugin_manager = PluginManager(plugin_dir, config)
    return _plugin_manager
