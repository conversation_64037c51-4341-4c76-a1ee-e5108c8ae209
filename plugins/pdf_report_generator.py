"""
PDF报告生成器插件
版本: 1.0
创建时间: 2025-06-26

生成PDF格式的睡眠音频分析报告
注意：需要安装reportlab库 (pip install reportlab)
"""

import io
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# 导入插件基类
import sys
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.platypus.tableofcontents import TableOfContents
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class PDFReportGenerator(ReportGeneratorPlugin):
    """PDF报告生成器插件"""
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': 'PDFReportGenerator',
            'version': '1.0',
            'description': '生成PDF格式的睡眠音频分析报告',
            'author': '智能睡眠音频评估系统',
            'supported_formats': ['pdf'],
            'dependencies': ['reportlab'],
            'available': REPORTLAB_AVAILABLE
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        if not REPORTLAB_AVAILABLE:
            self.logger.error("PDF插件需要reportlab库支持，请运行: pip install reportlab")
            return False
        return True
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的格式"""
        return ['pdf'] if REPORTLAB_AVAILABLE else []
    
    def get_required_dependencies(self) -> List[str]:
        """获取所需依赖"""
        return ['reportlab']
    
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """生成PDF报告"""
        if not REPORTLAB_AVAILABLE:
            return "错误：PDF生成需要reportlab库支持"
        
        try:
            # 获取配置参数
            title = config.get('title', '智能睡眠音频评估系统 - 分析报告')
            output_path = config.get('output_path', 'sleep_analysis_report.pdf')
            page_size = config.get('page_size', 'A4')
            
            # 创建PDF文档
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4 if page_size == 'A4' else letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 构建文档内容
            story = []
            styles = getSampleStyleSheet()
            
            # 添加自定义样式
            self._add_custom_styles(styles)
            
            # 添加标题页
            story.extend(self._create_title_page(title, styles))
            
            # 添加摘要
            story.extend(self._create_summary_section(results, styles))
            
            # 添加排名表
            story.extend(self._create_ranking_section(results, styles))
            
            # 添加详细分析
            story.extend(self._create_details_section(results, styles))
            
            # 构建PDF
            doc.build(story)
            
            # 保存到文件
            pdf_content = buffer.getvalue()
            buffer.close()
            
            with open(output_path, 'wb') as f:
                f.write(pdf_content)
            
            return f"PDF报告已生成: {output_path}"
            
        except Exception as e:
            self.logger.error(f"PDF报告生成失败: {e}")
            return f"PDF生成错误: {str(e)}"
    
    def _add_custom_styles(self, styles):
        """添加自定义样式"""
        # 标题样式
        styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2196F3')
        ))
        
        # 副标题样式
        styles.add(ParagraphStyle(
            name='CustomHeading2',
            parent=styles['Heading2'],
            fontSize=16,
            spaceBefore=20,
            spaceAfter=12,
            textColor=colors.HexColor('#2196F3')
        ))
        
        # 正文样式
        styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=6
        ))
    
    def _create_title_page(self, title: str, styles) -> List:
        """创建标题页"""
        story = []
        
        # 主标题
        story.append(Paragraph(title, styles['CustomTitle']))
        story.append(Spacer(1, 20))
        
        # 生成信息
        timestamp = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        story.append(Paragraph(f"生成时间: {timestamp}", styles['CustomNormal']))
        story.append(Paragraph("系统版本: 2.0", styles['CustomNormal']))
        story.append(Spacer(1, 40))
        
        return story
    
    def _create_summary_section(self, results: List[Any], styles) -> List:
        """创建摘要部分"""
        story = []
        
        story.append(Paragraph("📊 分析摘要", styles['CustomHeading2']))
        
        if not results:
            story.append(Paragraph("没有分析结果", styles['CustomNormal']))
            return story
        
        total_files = len(results)
        excellent_count = sum(1 for r in results if r.sleep_suitability.overall_score >= 80)
        good_count = sum(1 for r in results if 60 <= r.sleep_suitability.overall_score < 80)
        avg_score = sum(r.sleep_suitability.overall_score for r in results) / total_files
        
        # 创建摘要表格
        summary_data = [
            ['指标', '数值'],
            ['分析文件总数', str(total_files)],
            ['优秀文件 (≥80分)', str(excellent_count)],
            ['良好文件 (60-79分)', str(good_count)],
            ['平均得分', f'{avg_score:.1f}']
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2196F3')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _create_ranking_section(self, results: List[Any], styles) -> List:
        """创建排名部分"""
        story = []
        
        story.append(Paragraph("🏆 文件排名", styles['CustomHeading2']))
        
        if not results:
            story.append(Paragraph("没有分析结果", styles['CustomNormal']))
            return story
        
        # 按得分排序
        sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)
        
        # 创建排名表格
        ranking_data = [['排名', '文件名', '得分', '噪音类型', '安全等级', '推荐状态']]
        
        for i, result in enumerate(sorted_results, 1):
            filename = Path(result.audio_file).name
            score = result.sleep_suitability.overall_score
            noise_type = result.audio_features.noise_type.value
            safety = result.safety_assessment.overall_safety.value
            
            # 确定推荐状态
            if score >= 80:
                status = "强烈推荐"
            elif score >= 60:
                status = "可以使用"
            elif score >= 40:
                status = "谨慎使用"
            else:
                status = "不推荐"
            
            ranking_data.append([
                str(i),
                filename,
                f'{score:.1f}/100',
                noise_type,
                safety,
                status
            ])
        
        ranking_table = Table(ranking_data, colWidths=[0.5*inch, 2*inch, 1*inch, 1*inch, 1*inch, 1*inch])
        ranking_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2196F3')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8)
        ]))
        
        story.append(ranking_table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _create_details_section(self, results: List[Any], styles) -> List:
        """创建详细分析部分"""
        story = []
        
        story.append(Paragraph("📋 详细分析", styles['CustomHeading2']))
        
        if not results:
            story.append(Paragraph("没有分析结果", styles['CustomNormal']))
            return story
        
        for result in results:
            filename = Path(result.audio_file).name
            
            # 文件标题
            story.append(Paragraph(f"🎵 {filename}", styles['Heading3']))
            
            # 详细信息
            details = [
                f"睡眠适用性得分: {result.sleep_suitability.overall_score:.1f}/100",
                f"噪音类型: {result.audio_features.noise_type.value}",
                f"安全等级: {result.safety_assessment.overall_safety.value}",
                f"效果预测: {result.sleep_suitability.effectiveness_prediction:.1%}",
                f"总体推荐: {result.overall_recommendation}"
            ]
            
            for detail in details:
                story.append(Paragraph(detail, styles['CustomNormal']))
            
            story.append(Spacer(1, 10))
        
        return story
