"""
CSV报告生成器插件
版本: 1.0
创建时间: 2025-06-26

生成CSV格式的睡眠音频分析报告，便于数据分析和处理
"""

import csv
import io
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# 导入插件基类
import sys
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin


class CSVReportGenerator(ReportGeneratorPlugin):
    """CSV报告生成器插件"""
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': 'CSVReportGenerator',
            'version': '1.0',
            'description': '生成CSV格式的睡眠音频分析报告，便于数据分析',
            'author': '智能睡眠音频评估系统',
            'supported_formats': ['csv'],
            'dependencies': []
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        return True
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的格式"""
        return ['csv']
    
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """生成CSV报告"""
        try:
            # 获取配置参数
            output_path = config.get('output_path', 'sleep_analysis_report.csv')
            include_detailed = config.get('include_detailed', True)
            delimiter = config.get('delimiter', ',')
            
            # 生成CSV内容
            csv_content = self._generate_csv_content(results, include_detailed, delimiter)
            
            # 保存到文件
            with open(output_path, 'w', encoding='utf-8', newline='') as f:
                f.write(csv_content)
            
            return f"CSV报告已生成: {output_path}"
            
        except Exception as e:
            self.logger.error(f"CSV报告生成失败: {e}")
            return f"CSV生成错误: {str(e)}"
    
    def _generate_csv_content(self, results: List[Any], include_detailed: bool, delimiter: str) -> str:
        """生成CSV内容"""
        if not results:
            return "文件名,错误信息\n,没有分析结果"
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output, delimiter=delimiter)
        
        # 写入标题行
        if include_detailed:
            headers = [
                '排名',
                '文件名',
                '文件路径',
                '睡眠适用性得分',
                '噪音类型',
                '安全等级',
                '效果预测(%)',
                '推荐状态',
                '总体推荐',
                '响度稳定性',
                '音调比例',
                '动态范围(dB)',
                '频谱斜率',
                '分析时间',
                '系统版本'
            ]
        else:
            headers = [
                '排名',
                '文件名',
                '睡眠适用性得分',
                '噪音类型',
                '安全等级',
                '推荐状态'
            ]
        
        writer.writerow(headers)
        
        # 按得分排序
        sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)
        
        # 写入数据行
        for i, result in enumerate(sorted_results, 1):
            filename = Path(result.audio_file).name
            score = result.sleep_suitability.overall_score
            noise_type = result.audio_features.noise_type.value
            safety = result.safety_assessment.overall_safety.value
            effectiveness = result.sleep_suitability.effectiveness_prediction * 100
            
            # 确定推荐状态
            if score >= 80:
                status = "强烈推荐"
            elif score >= 60:
                status = "可以使用"
            elif score >= 40:
                status = "谨慎使用"
            else:
                status = "不推荐"
            
            if include_detailed:
                row = [
                    i,  # 排名
                    filename,  # 文件名
                    result.audio_file,  # 文件路径
                    f"{score:.1f}",  # 睡眠适用性得分
                    noise_type,  # 噪音类型
                    safety,  # 安全等级
                    f"{effectiveness:.1f}",  # 效果预测
                    status,  # 推荐状态
                    result.overall_recommendation,  # 总体推荐
                    f"{result.audio_features.loudness_stability:.3f}",  # 响度稳定性
                    f"{result.audio_features.tonal_ratio:.1f}",  # 音调比例
                    f"{result.audio_features.dynamic_range_db:.1f}" if result.audio_features.dynamic_range_db else "N/A",  # 动态范围
                    f"{result.audio_features.spectral_slope:.3f}" if result.audio_features.spectral_slope else "N/A",  # 频谱斜率
                    result.analysis_timestamp,  # 分析时间
                    "2.0"  # 系统版本
                ]
            else:
                row = [
                    i,  # 排名
                    filename,  # 文件名
                    f"{score:.1f}",  # 睡眠适用性得分
                    noise_type,  # 噪音类型
                    safety,  # 安全等级
                    status  # 推荐状态
                ]
            
            writer.writerow(row)
        
        # 添加摘要信息（作为注释行）
        if include_detailed:
            writer.writerow([])  # 空行
            writer.writerow(['# 分析摘要'])
            writer.writerow(['# 总文件数', len(results)])
            writer.writerow(['# 优秀文件数 (≥80分)', sum(1 for r in results if r.sleep_suitability.overall_score >= 80)])
            writer.writerow(['# 良好文件数 (60-79分)', sum(1 for r in results if 60 <= r.sleep_suitability.overall_score < 80)])
            writer.writerow(['# 平均得分', f"{sum(r.sleep_suitability.overall_score for r in results) / len(results):.1f}"])
            writer.writerow(['# 生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
        
        csv_content = output.getvalue()
        output.close()
        
        return csv_content


class ExcelReportGenerator(ReportGeneratorPlugin):
    """Excel报告生成器插件（需要openpyxl库）"""
    
    def __init__(self, config=None):
        super().__init__(config)
        try:
            import openpyxl
            self.openpyxl_available = True
        except ImportError:
            self.openpyxl_available = False
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': 'ExcelReportGenerator',
            'version': '1.0',
            'description': '生成Excel格式的睡眠音频分析报告',
            'author': '智能睡眠音频评估系统',
            'supported_formats': ['xlsx', 'excel'],
            'dependencies': ['openpyxl'],
            'available': self.openpyxl_available
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        if not self.openpyxl_available:
            self.logger.error("Excel插件需要openpyxl库支持，请运行: pip install openpyxl")
            return False
        return True
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的格式"""
        return ['xlsx', 'excel'] if self.openpyxl_available else []
    
    def get_required_dependencies(self) -> List[str]:
        """获取所需依赖"""
        return ['openpyxl']
    
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """生成Excel报告"""
        if not self.openpyxl_available:
            return "错误：Excel生成需要openpyxl库支持"
        
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # 获取配置参数
            output_path = config.get('output_path', 'sleep_analysis_report.xlsx')
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "睡眠音频分析报告"
            
            # 设置标题
            ws['A1'] = '智能睡眠音频评估系统 - 分析报告'
            ws['A1'].font = Font(size=16, bold=True, color='2196F3')
            ws.merge_cells('A1:F1')
            
            # 设置表头
            headers = ['排名', '文件名', '睡眠适用性得分', '噪音类型', '安全等级', '推荐状态']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=3, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='E3F2FD', end_color='E3F2FD', fill_type='solid')
                cell.alignment = Alignment(horizontal='center')
            
            # 填充数据
            if results:
                sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)
                
                for i, result in enumerate(sorted_results, 1):
                    row = i + 3
                    filename = Path(result.audio_file).name
                    score = result.sleep_suitability.overall_score
                    noise_type = result.audio_features.noise_type.value
                    safety = result.safety_assessment.overall_safety.value
                    
                    # 确定推荐状态
                    if score >= 80:
                        status = "强烈推荐"
                    elif score >= 60:
                        status = "可以使用"
                    elif score >= 40:
                        status = "谨慎使用"
                    else:
                        status = "不推荐"
                    
                    ws.cell(row=row, column=1, value=i)
                    ws.cell(row=row, column=2, value=filename)
                    ws.cell(row=row, column=3, value=f"{score:.1f}/100")
                    ws.cell(row=row, column=4, value=noise_type)
                    ws.cell(row=row, column=5, value=safety)
                    ws.cell(row=row, column=6, value=status)
            
            # 调整列宽
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 30
            ws.column_dimensions['C'].width = 15
            ws.column_dimensions['D'].width = 12
            ws.column_dimensions['E'].width = 12
            ws.column_dimensions['F'].width = 12
            
            # 保存文件
            wb.save(output_path)
            
            return f"Excel报告已生成: {output_path}"
            
        except Exception as e:
            self.logger.error(f"Excel报告生成失败: {e}")
            return f"Excel生成错误: {str(e)}"
