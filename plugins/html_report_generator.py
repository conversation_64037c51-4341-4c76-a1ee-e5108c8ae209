"""
HTML报告生成器插件
版本: 1.0
创建时间: 2025-06-26

生成美观的HTML格式睡眠音频分析报告
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# 导入插件基类
import sys
sys.path.append(str(Path(__file__).parent.parent))
from plugin_system import ReportGeneratorPlugin


class HTMLReportGenerator(ReportGeneratorPlugin):
    """HTML报告生成器插件"""
    
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': 'HTMLReportGenerator',
            'version': '1.0',
            'description': '生成美观的HTML格式睡眠音频分析报告',
            'author': '智能睡眠音频评估系统',
            'supported_formats': ['html'],
            'dependencies': []
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        # 基本配置验证
        return True
    
    def get_supported_formats(self) -> List[str]:
        """返回支持的格式"""
        return ['html']
    
    def generate_report(self, results: List[Any], config: Dict[str, Any]) -> str:
        """生成HTML报告"""
        try:
            # 获取配置参数
            title = config.get('title', '智能睡眠音频评估系统 - 分析报告')
            template_style = config.get('template_style', 'modern')
            include_charts = config.get('include_charts', True)
            
            # 生成HTML内容
            html_content = self._generate_html_template(results, title, template_style, include_charts)

            # 如果指定了输出路径，保存到文件
            output_path = config.get('output_path')
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                return f"HTML报告已保存到: {output_path}"

            return html_content
            
        except Exception as e:
            self.logger.error(f"HTML报告生成失败: {e}")
            return self._generate_error_report(str(e))
    
    def _generate_html_template(self, results: List[Any], title: str, 
                               template_style: str, include_charts: bool) -> str:
        """生成HTML模板"""
        
        # CSS样式
        css_styles = self._get_css_styles(template_style)
        
        # 生成报告内容
        summary_section = self._generate_summary_section(results)
        ranking_section = self._generate_ranking_section(results)
        details_section = self._generate_details_section(results)
        
        # 图表部分（如果启用）
        charts_section = ""
        if include_charts:
            charts_section = self._generate_charts_section(results)
        
        # 组装完整HTML
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        {css_styles}
    </style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{title}</h1>
            <div class="report-meta">
                <span class="timestamp">生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</span>
                <span class="version">系统版本: 2.0 (专业增强版)</span>
            </div>
        </header>

        <div class="view-toggle">
            <button class="toggle-btn active" onclick="toggleView('professional')">🔬 专业视图</button>
            <button class="toggle-btn" onclick="toggleView('simple')">📋 简化视图</button>
        </div>
        
        <main class="report-content">
            {summary_section}
            {ranking_section}
            {charts_section}
            {details_section}
        </main>
        
        <footer class="report-footer">
            <p>© 2025 智能睡眠音频评估系统 - 基于科学研究的睡眠音频分析</p>
            <p><strong>报告生成</strong>: 智能睡眠音频评估与推荐系统 v2.0 (专业增强版)</p>
        </footer>
    </div>

    <script>
        // 视图切换功能
        function toggleView(viewType) {{
            const professionalElements = document.querySelectorAll('.professional-view');
            const simpleElements = document.querySelectorAll('.simple-view');
            const buttons = document.querySelectorAll('.toggle-btn');

            buttons.forEach(btn => btn.classList.remove('active'));

            if (viewType === 'professional') {{
                professionalElements.forEach(el => el.style.display = 'block');
                simpleElements.forEach(el => el.style.display = 'none');
                document.querySelector('button[onclick*="professional"]').classList.add('active');
            }} else {{
                professionalElements.forEach(el => el.style.display = 'none');
                simpleElements.forEach(el => el.style.display = 'block');
                document.querySelector('button[onclick*="simple"]').classList.add('active');
            }}
        }}

        // 初始化专业视图
        document.addEventListener('DOMContentLoaded', function() {{
            toggleView('professional');
        }});
    </script>
</body>
</html>
        """
        
        return html_template.strip()
    
    def _get_css_styles(self, template_style: str) -> str:
        """获取CSS样式"""
        if template_style == 'modern':
            return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #2196F3;
            margin-bottom: 30px;
        }
        
        .report-header h1 {
            color: #2196F3;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .report-meta {
            color: #666;
            font-size: 0.9em;
        }
        
        .report-meta span {
            margin: 0 15px;
        }

        .view-toggle {
            text-align: center;
            margin: 20px 0;
        }

        .toggle-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .toggle-btn:hover {
            background: #1976D2;
        }

        .toggle-btn.active {
            background: #1976D2;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        
        .section h2 {
            color: #2196F3;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary-card h3 {
            color: #2196F3;
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .ranking-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ranking-table th,
        .ranking-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .ranking-table th {
            background: #2196F3;
            color: white;
            font-weight: 600;
        }
        
        .ranking-table tr:hover {
            background: #f5f5f5;
        }
        
        .score-excellent { color: #4CAF50; font-weight: bold; }
        .score-good { color: #2196F3; font-weight: bold; }
        .score-fair { color: #FF9800; font-weight: bold; }
        .score-poor { color: #F44336; font-weight: bold; }
        
        .status-excellent { background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-good { background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-fair { background: #FF9800; color: white; padding: 4px 8px; border-radius: 4px; }
        .status-poor { background: #F44336; color: white; padding: 4px 8px; border-radius: 4px; }
        
        .report-footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 30px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .report-header h1 {
                font-size: 2em;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
            """
        else:
            # 简单样式
            return """
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .report-header { text-align: center; margin-bottom: 30px; }
        .section { margin: 20px 0; }
        .ranking-table { width: 100%; border-collapse: collapse; }
        .ranking-table th, .ranking-table td { padding: 8px; border: 1px solid #ddd; }
        .ranking-table th { background: #f0f0f0; }
            """

    def _generate_summary_section(self, results: List[Any]) -> str:
        """生成摘要部分"""
        if not results:
            return '<div class="section"><h2>📊 分析摘要</h2><p>没有分析结果</p></div>'

        total_files = len(results)
        excellent_count = sum(1 for r in results if r.sleep_suitability.overall_score >= 80)
        good_count = sum(1 for r in results if 60 <= r.sleep_suitability.overall_score < 80)

        avg_score = sum(r.sleep_suitability.overall_score for r in results) / total_files

        return f"""
        <div class="section">
            <h2>📊 分析摘要</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>{total_files}</h3>
                    <p>分析文件总数</p>
                </div>
                <div class="summary-card">
                    <h3>{excellent_count}</h3>
                    <p>优秀文件 (≥80分)</p>
                </div>
                <div class="summary-card">
                    <h3>{good_count}</h3>
                    <p>良好文件 (60-79分)</p>
                </div>
                <div class="summary-card">
                    <h3>{avg_score:.1f}</h3>
                    <p>平均得分</p>
                </div>
            </div>
        </div>
        """

    def _generate_ranking_section(self, results: List[Any]) -> str:
        """生成排名部分"""
        if not results:
            return '<div class="section"><h2>🏆 文件排名</h2><p>没有分析结果</p></div>'

        # 按得分排序
        sorted_results = sorted(results, key=lambda x: x.sleep_suitability.overall_score, reverse=True)

        table_rows = ""
        for i, result in enumerate(sorted_results, 1):
            filename = Path(result.audio_file).name
            score = result.sleep_suitability.overall_score
            noise_type = result.audio_features.noise_type.value
            safety = result.safety_assessment.overall_safety.value

            # 确定得分样式
            if score >= 80:
                score_class = "score-excellent"
                status = '<span class="status-excellent">✅ 强烈推荐</span>'
            elif score >= 60:
                score_class = "score-good"
                status = '<span class="status-good">⚠️ 可以使用</span>'
            elif score >= 40:
                score_class = "score-fair"
                status = '<span class="status-fair">🤔 谨慎使用</span>'
            else:
                score_class = "score-poor"
                status = '<span class="status-poor">❌ 不推荐</span>'

            table_rows += f"""
            <tr>
                <td>{i}</td>
                <td><strong>{filename}</strong></td>
                <td class="{score_class}">{score:.1f}/100</td>
                <td>{noise_type}</td>
                <td>{safety}</td>
                <td>{status}</td>
            </tr>
            """

        return f"""
        <div class="section">
            <h2>🏆 文件排名</h2>
            <table class="ranking-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>文件名</th>
                        <th>睡眠适用性得分</th>
                        <th>噪音类型</th>
                        <th>安全等级</th>
                        <th>推荐状态</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>
        </div>
        """

    def _generate_details_section(self, results: List[Any]) -> str:
        """生成详细信息部分"""
        if not results:
            return '<div class="section"><h2>📋 详细分析</h2><p>没有分析结果</p></div>'

        details_content = ""
        for result in results:
            filename = Path(result.audio_file).name
            details_content += f"""
            <div class="file-detail">
                <h3>🎵 {filename}</h3>
                <div class="detail-grid">
                    <div><strong>睡眠适用性得分:</strong> {result.sleep_suitability.overall_score:.1f}/100</div>
                    <div><strong>噪音类型:</strong> {result.audio_features.noise_type.value}</div>
                    <div><strong>安全等级:</strong> {result.safety_assessment.overall_safety.value}</div>
                    <div><strong>效果预测:</strong> {result.sleep_suitability.effectiveness_prediction:.1%}</div>
                </div>
                <p><strong>总体推荐:</strong> {result.overall_recommendation}</p>
            </div>
            """

        return f"""
        <div class="section">
            <h2>📋 详细分析</h2>
            {details_content}
        </div>
        """

    def _generate_charts_section(self, results: List[Any]) -> str:
        """生成图表部分（简化版本）"""
        return """
        <div class="section">
            <h2>📈 数据可视化</h2>
            <p><em>图表功能将在后续版本中提供</em></p>
        </div>
        """

    def _generate_error_report(self, error_message: str) -> str:
        """生成错误报告"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>报告生成错误</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .error {{ color: red; background: #ffe6e6; padding: 20px; border-radius: 5px; }}
    </style>
</head>
<body>
    <h1>报告生成错误</h1>
    <div class="error">
        <h2>错误信息:</h2>
        <p>{error_message}</p>
    </div>
</body>
</html>
        """
